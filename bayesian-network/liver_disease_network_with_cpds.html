
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liver Disease Diagnosis - Interactive Bayesian Network</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: white;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #fff, #e8eaff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
            margin: 5px 0;
        }

        .controls-section {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 15px;
            margin: 20px auto;
            max-width: 1200px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            color: #2d3748;
        }

        .controls-section h3 {
            margin: 0 0 20px 0;
            text-align: center;
            font-size: 1.4em;
            color: #4a5568;
        }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .control-group {
            background: linear-gradient(135deg, #f7fafc, #edf2f7);
            padding: 20px;
            border-radius: 12px;
            border: 2px solid #e2e8f0;
        }

        .control-label {
            display: block;
            font-weight: 600;
            font-size: 14px;
            color: #2d3748;
            margin-bottom: 8px;
        }

        .control-description {
            font-size: 12px;
            color: #718096;
            margin-bottom: 12px;
        }

        .slider-container {
            position: relative;
            margin-bottom: 10px;
        }

        .slider {
            -webkit-appearance: none;
            width: 100%;
            height: 8px;
            border-radius: 5px;
            background: #cbd5e0;
            outline: none;
            transition: background 0.3s;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        }

        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        }

        .slider-value {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: 600;
            min-width: 60px;
            text-align: center;
        }

        .reset-button {
            background: linear-gradient(135deg, #718096, #4a5568);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .reset-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .evidence-select {
            transition: all 0.3s ease;
        }

        .evidence-select:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            outline: none;
        }

        .evidence-select:hover {
            border-color: #a0aec0;
        }

        .evidence-status {
            transition: color 0.3s ease;
        }

        .server-status {
            text-align: center;
            padding: 10px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: 600;
        }

        .server-status.connected {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }

        .server-status.disconnected {
            background: linear-gradient(135deg, #f56565, #e53e3e);
            color: white;
        }

        .server-status.connecting {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
            color: white;
        }

        .legend {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 15px;
            margin: 20px auto;
            max-width: 900px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            color: #2d3748;
        }

        .legend h3 {
            margin: 0 0 20px 0;
            text-align: center;
            font-size: 1.3em;
        }

        .legend-items {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .legend-item {
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .risk-factor { background: linear-gradient(135deg, #ffebee, #ffcdd2); color: #c62828; }
        .biomarker { background: linear-gradient(135deg, #e8f5e8, #c8e6c9); color: #2e7d32; }
        .outcome { background: linear-gradient(135deg, #f3e5f5, #e1bee7); color: #7b1fa2; }

        .network-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            margin: 0 auto;
            max-width: 1200px;
            overflow: hidden;
            position: relative;
            width: 100%;
            height: 700px;
        }

        #network {
            width: 100%;
            height: 100%;
            display: block;
        }

        .tooltip {
            position: absolute;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.3);
            color: white;
            min-width: 300px;
            max-width: 90vw;
            max-height: 80vh;
            z-index: 1000;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
            pointer-events: none;
            backdrop-filter: blur(10px);
            overflow: auto;
        }

        .tooltip.show {
            opacity: 1;
            transform: translateY(0);
        }

        .tooltip h3 {
            margin: 0 0 15px 0;
            font-size: 18px;
            text-align: center;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            padding-bottom: 10px;
        }

        .tooltip h4 {
            margin: 0 0 15px 0;
            font-size: 16px;
            text-align: center;
            color: #e8eaff;
        }

        .cpd-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            overflow: hidden;
            table-layout: auto;
        }

        .cpd-table th {
            background: rgba(255,255,255,0.2);
            color: white;
            font-weight: 600;
            padding: 8px 6px;
            text-align: center;
            font-size: 10px;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            white-space: normal;
            word-wrap: break-word;
            line-height: 1.2;
            min-width: 80px;
        }

        .cpd-table td {
            padding: 8px 6px;
            text-align: center;
            font-size: 11px;
            font-weight: 500;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(255,255,255,0.05);
            white-space: nowrap;
        }

        .cpd-table.wide-table th {
            font-size: 9px;
            padding: 6px 4px;
        }

        .cpd-table.wide-table td {
            font-size: 10px;
            padding: 6px 4px;
        }

        .cpd-table tr:hover td {
            background: rgba(255,255,255,0.15);
        }

        .cpd-table tbody tr:last-child td {
            border-bottom: none;
        }

        .instructions {
            text-align: center;
            margin-top: 30px;
            font-size: 1.1em;
            opacity: 0.9;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
            animation: fadeIn 0.3s ease;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 30px;
            max-width: 95vw;
            max-height: 90vh;
            overflow: auto;
            box-shadow: 0 25px 80px rgba(0,0,0,0.4);
            color: white;
            position: relative;
            animation: slideIn 0.3s ease;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            padding-bottom: 15px;
        }

        .modal-title {
            font-size: 24px;
            margin: 0;
            background: linear-gradient(45deg, #fff, #e8eaff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .close-button {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            font-size: 24px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .close-button:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        .modal-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            overflow: hidden;
            margin-top: 20px;
        }

        .modal-table th {
            background: rgba(255,255,255,0.2);
            color: white;
            font-weight: 600;
            padding: 15px 12px;
            text-align: center;
            font-size: 14px;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            white-space: normal;
            word-wrap: break-word;
            line-height: 1.3;
            vertical-align: middle;
        }

        .modal-table td {
            padding: 12px;
            text-align: center;
            font-size: 14px;
            font-weight: 500;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(255,255,255,0.05);
            white-space: nowrap;
        }

        .modal-table tr:hover td {
            background: rgba(255,255,255,0.15);
        }

        .modal-table tbody tr:last-child td {
            border-bottom: none;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { 
                opacity: 0; 
                transform: translateY(-50px) scale(0.9); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0) scale(1); 
            }
        }

        .instructions strong {
            color: #fff;
        }

        .node {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .node-circle {
            r: 40;
        }

        .node-rect {
            width: 100;
            height: 70;
            rx: 10;
            ry: 10;
        }

        .node-diamond {
            transform-origin: center;
        }

        .node-text {
            text-anchor: middle;
            dominant-baseline: middle;
            fill: white;
            font-weight: 600;
            font-size: 12px;
            pointer-events: none;
        }

        .node-icon {
            text-anchor: middle;
            dominant-baseline: middle;
            fill: white;
            font-size: 20px;
            pointer-events: none;
        }

        .edge {
            stroke: #4a5568;
            stroke-width: 3;
            fill: none;
            marker-end: url(#arrowhead);
        }

        .node-probability {
            text-anchor: middle;
            dominant-baseline: middle;
            fill: white;
            font-weight: 600;
            font-size: 10px;
            pointer-events: none;
        }

        .probability-updated {
            animation: probabilityPulse 1s ease-in-out;
        }

        @keyframes probabilityPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏥 Interactive Liver Disease Diagnosis</h1>
        <p>Bayesian Network with Real-time Inference</p>
        <p style="font-size: 1em; opacity: 0.8;">Adjust Prior Probabilities and Watch the Network Update</p>
    </div>

    <div class="controls-section">
        <h3>🎛️ Interactive Controls: Prior Probabilities & Evidence</h3>
        <div id="server-status" class="server-status connecting">🔄 Connecting to inference server...</div>
        <div id="controls-grid" class="controls-grid">
            <!-- Controls will be populated dynamically -->
        </div>
        <div style="text-align: center; margin-top: 15px;">
            <button onclick="resetToDefaults()" class="reset-button">🔄 Reset to Defaults</button>
        </div>
    </div>

    <div class="legend">
        <h3>📋 Network Components</h3>
        <div class="legend-items">
            <div class="legend-item risk-factor">🍺 Risk Factors</div>
            <div class="legend-item biomarker">🧪 Laboratory Biomarkers</div>
            <div class="legend-item outcome">⚕️ Clinical Diagnosis</div>
        </div>
    </div>

    <div class="network-container">
        <svg id="network" viewBox="0 0 800 600" preserveAspectRatio="xMidYMid meet">
            <defs>
                <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                        refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="#4a5568"/>
                </marker>
                
                <!-- Gradients for nodes -->
                <linearGradient id="alcoholGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#ff5252;stop-opacity:1" />
                </linearGradient>
                
                <linearGradient id="liverGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#ff9800;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#f57c00;stop-opacity:1" />
                </linearGradient>
                
                <linearGradient id="astGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#4caf50;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#388e3c;stop-opacity:1" />
                </linearGradient>
                
                <linearGradient id="ggtGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#2196f3;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#1976d2;stop-opacity:1" />
                </linearGradient>
                
                <linearGradient id="diagnosisGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#9c27b0;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#7b1fa2;stop-opacity:1" />
                </linearGradient>
            </defs>
        </svg>
        <div id="tooltip" class="tooltip"></div>
    </div>

    <div class="instructions">
        <p>💡 <strong>Interactive Features:</strong> Use sliders to adjust prior probabilities • Use dropdowns to set evidence (observed values) • Hover over nodes for quick probability view • Click nodes for detailed probability tables • Drag nodes to rearrange the network • Watch real-time probability updates!</p>
    </div>

    <!-- Modal for detailed probability tables -->
    <div id="probabilityModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="modalTitle"></h2>
                <button class="close-button" onclick="closeModal()">&times;</button>
            </div>
            <div id="modalTableContainer"></div>
        </div>
    </div>

    <script>
        // Configuration
        const API_BASE_URL = 'http://localhost:5000/api';
        
        // Global state
        let currentPriors = {};
        let currentEvidence = {};
        let inferenceResults = {};
        let isServerConnected = false;
        let controllableVariables = [];
        let evidenceVariables = [];
        
        // Data from Python - using normalized coordinates (0-1 range)
        const nodes = [
            {
                'id': 'Alcohol_Abuse',
                'x': 0.25, 'y': 0.2,
                'color': 'url(#alcoholGradient)',
                'icon': '🍺',
                'label': 'Alcohol Abuse',
                'type': 'risk-factor',
                'shape': 'circle'
            },
            {
                'id': 'Liver_Disease',
                'x': 0.75, 'y': 0.2,
                'color': 'url(#liverGradient)',
                'icon': '🏥',
                'label': 'Liver Disease',
                'type': 'risk-factor',
                'shape': 'circle'
            },
            {
                'id': 'AST_ALT_Ratio',
                'x': 0.25, 'y': 0.5,
                'color': 'url(#astGradient)',
                'icon': '📊',
                'label': 'AST/ALT Ratio',
                'type': 'biomarker',
                'shape': 'rect'
            },
            {
                'id': 'GGT',
                'x': 0.75, 'y': 0.5,
                'color': 'url(#ggtGradient)',
                'icon': '🧪',
                'label': 'GGT Level',
                'type': 'biomarker',
                'shape': 'rect'
            },
            {
                'id': 'Diagnosis',
                'x': 0.5, 'y': 0.8,
                'color': 'url(#diagnosisGradient)',
                'icon': '⚕️',
                'label': 'Final Diagnosis',
                'type': 'outcome',
                'shape': 'diamond'
            }
        ];
        
        const edges = [{"from": "Alcohol_Abuse", "to": "AST_ALT_Ratio"}, {"from": "Alcohol_Abuse", "to": "GGT"}, {"from": "Liver_Disease", "to": "AST_ALT_Ratio"}, {"from": "Liver_Disease", "to": "GGT"}, {"from": "AST_ALT_Ratio", "to": "Diagnosis"}, {"from": "GGT", "to": "Diagnosis"}];

        // Initialize the network
        const svg = document.getElementById('network');
        const tooltip = document.getElementById('tooltip');
        let isDragging = false;
        let currentNode = null;
        
        // Server communication functions
        async function checkServerConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/controllable_variables`);
                if (response.ok) {
                    const data = await response.json();
                    controllableVariables = data.priors || [];
                    evidenceVariables = data.evidence || [];
                    isServerConnected = true;
                    updateServerStatus('connected', '✅ Server connected - Interactive controls active');
                    await initializeControls();
                    await loadInitialProbabilities();
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                isServerConnected = false;
                updateServerStatus('disconnected', '❌ Server disconnected - Start backend_server.py to enable interactive features');
                console.error('Server connection failed:', error);
            }
        }

        function updateServerStatus(status, message) {
            const statusElement = document.getElementById('server-status');
            statusElement.className = `server-status ${status}`;
            statusElement.textContent = message;
        }

        async function performInference() {
            if (!isServerConnected) return;

            console.log('Performing inference with:');
            console.log('  Priors:', currentPriors);
            console.log('  Evidence:', currentEvidence);

            try {
                const response = await fetch(`${API_BASE_URL}/inference`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        priors: currentPriors,
                        evidence: currentEvidence
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('Inference response:', data);
                    if (data.success) {
                        inferenceResults = data.results;
                        updateNodeProbabilities();
                    } else {
                        console.error('Inference failed:', data.error);
                    }
                } else {
                    console.error('Server responded with error:', response.status);
                }
            } catch (error) {
                console.error('Inference failed:', error);
            }
        }

        async function loadInitialProbabilities() {
            if (!isServerConnected) return;

            try {
                const response = await fetch(`${API_BASE_URL}/marginals`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        inferenceResults = data.results;
                        updateNodeProbabilities();
                    }
                }
            } catch (error) {
                console.error('Failed to load initial probabilities:', error);
            }
        }

        function initializeControls() {
            const controlsGrid = document.getElementById('controls-grid');
            controlsGrid.innerHTML = '';

            // Add prior probability sliders
            controllableVariables.forEach(variable => {
                const controlGroup = document.createElement('div');
                controlGroup.className = 'control-group';
                
                controlGroup.innerHTML = `
                    <label class="control-label">🎛️ ${variable.name}</label>
                    <div class="control-description">${variable.description}</div>
                    <div class="slider-container">
                        <input type="range" 
                               class="slider" 
                               id="slider-${variable.id}"
                               min="${variable.min}" 
                               max="${variable.max}" 
                               step="${variable.step}" 
                               value="${variable.default}"
                               oninput="onSliderChange('${variable.id}', this.value)">
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-size: 12px; color: #718096;">0.00</span>
                        <span class="slider-value" id="value-${variable.id}">${variable.default.toFixed(2)}</span>
                        <span style="font-size: 12px; color: #718096;">1.00</span>
                    </div>
                `;
                
                controlsGrid.appendChild(controlGroup);
                currentPriors[variable.id] = variable.default;
            });

            // Add evidence controls (dropdowns)
            evidenceVariables.forEach(variable => {
                const controlGroup = document.createElement('div');
                controlGroup.className = 'control-group';
                
                const options = ['Not Observed', ...variable.states].map((state, index) => 
                    `<option value="${index === 0 ? '' : state}">${state}</option>`
                ).join('');
                
                controlGroup.innerHTML = `
                    <label class="control-label">🔬 ${variable.name}</label>
                    <div class="control-description">${variable.description}</div>
                    <div style="margin: 10px 0;">
                        <select class="evidence-select" 
                                id="evidence-${variable.id}"
                                onchange="onEvidenceChange('${variable.id}', this.value)"
                                style="width: 100%; padding: 8px 12px; border-radius: 8px; border: 2px solid #e2e8f0; background: white; font-size: 14px;">
                            ${options}
                        </select>
                    </div>
                    <div style="text-align: center; margin-top: 8px;">
                        <span class="evidence-status" id="status-${variable.id}" style="font-size: 12px; color: #718096; font-weight: 600;">
                            Status: Not Observed
                        </span>
                    </div>
                `;
                
                controlsGrid.appendChild(controlGroup);
                // Don't initialize evidence with null/default values
                // Evidence will be added only when explicitly set
            });
        }

        function onSliderChange(variableId, value) {
            const numValue = parseFloat(value);
            currentPriors[variableId] = numValue;
            
            // Update display
            const valueElement = document.getElementById(`value-${variableId}`);
            valueElement.textContent = numValue.toFixed(2);
            
            // Perform inference with debouncing
            clearTimeout(window.inferenceTimeout);
            window.inferenceTimeout = setTimeout(() => {
                performInference();
            }, 300);
        }

        function onEvidenceChange(variableId, value) {
            console.log(`Evidence change for ${variableId}: "${value}"`);
            
            if (value === '' || value === 'Not Observed') {
                // Not observed - remove from evidence
                if (currentEvidence.hasOwnProperty(variableId)) {
                    delete currentEvidence[variableId];
                }
                const statusElement = document.getElementById(`status-${variableId}`);
                statusElement.textContent = 'Status: Not Observed';
                statusElement.style.color = '#718096';
            } else {
                // Observed - add to evidence
                currentEvidence[variableId] = value;
                const statusElement = document.getElementById(`status-${variableId}`);
                statusElement.textContent = `Status: Observed as "${value}"`;
                statusElement.style.color = '#2d3748';
            }
            
            console.log('Current evidence state:', currentEvidence);
            
            // Perform inference with debouncing
            clearTimeout(window.inferenceTimeout);
            window.inferenceTimeout = setTimeout(() => {
                performInference();
            }, 300);
        }

        function resetToDefaults() {
            // Reset prior sliders
            controllableVariables.forEach(variable => {
                const slider = document.getElementById(`slider-${variable.id}`);
                const valueElement = document.getElementById(`value-${variable.id}`);
                
                slider.value = variable.default;
                valueElement.textContent = variable.default.toFixed(2);
                currentPriors[variable.id] = variable.default;
            });
            
            // Reset evidence dropdowns
            evidenceVariables.forEach(variable => {
                const dropdown = document.getElementById(`evidence-${variable.id}`);
                const statusElement = document.getElementById(`status-${variable.id}`);
                
                dropdown.value = '';
                statusElement.textContent = 'Status: Not Observed';
                statusElement.style.color = '#718096';
                if (currentEvidence.hasOwnProperty(variable.id)) {
                    delete currentEvidence[variable.id];
                }
            });
            
            console.log('Reset - Current evidence state:', currentEvidence);
            console.log('Reset - Current priors state:', currentPriors);
            
            performInference();
        }

        function updateNodeProbabilities() {
            nodes.forEach(node => {
                const nodeElement = svg.querySelector(`[data-id="${node.id}"]`);
                if (nodeElement) {
                    // Find or create probability text element
                    let probText = nodeElement.querySelector('.node-probability');
                    if (!probText) {
                        probText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                        probText.setAttribute('class', 'node-probability');
                        probText.setAttribute('y', '28');
                        nodeElement.appendChild(probText);
                    }
                    
                    // Check if this variable is observed (evidence)
                    if (currentEvidence[node.id]) {
                        probText.textContent = `Observed: ${currentEvidence[node.id]}`;
                        probText.style.fill = '#f56565'; // Red for observed
                    } else if (inferenceResults[node.id]) {
                        const result = inferenceResults[node.id];
                        
                        // For binary variables, show the probability of the positive state
                        if (result.states.length === 2) {
                            const positiveStateIndex = result.states.indexOf('Yes') !== -1 ? 
                                result.states.indexOf('Yes') : 1;
                            const probability = result.probabilities[positiveStateIndex];
                            probText.textContent = `${(probability * 100).toFixed(1)}%`;
                        } else {
                            // For multi-state variables, show the most likely state
                            const maxIndex = result.probabilities.indexOf(Math.max(...result.probabilities));
                            const probability = result.probabilities[maxIndex];
                            probText.textContent = `${(probability * 100).toFixed(1)}%`;
                        }
                        probText.style.fill = 'white'; // White for inferred
                    }
                    
                    // Add pulse animation to indicate update
                    probText.classList.add('probability-updated');
                    setTimeout(() => {
                        probText.classList.remove('probability-updated');
                    }, 1000);
                }
            });
        }
        
        // Get SVG dimensions
        function getSVGDimensions() {
            const rect = svg.getBoundingClientRect();
            return { width: rect.width, height: rect.height };
        }
        
        // Convert normalized coordinates to SVG coordinates
        function normalizedToSVG(normalizedX, normalizedY) {
            return {
                x: normalizedX * 800,
                y: normalizedY * 600
            };
        }
        
        // Convert SVG coordinates to normalized coordinates
        function svgToNormalized(svgX, svgY) {
            return {
                x: svgX / 800,
                y: svgY / 600
            };
        }

        // Create edges first (so they appear behind nodes)
        function createEdges() {
            edges.forEach(edge => {
                const fromNode = nodes.find(n => n.id === edge.from);
                const toNode = nodes.find(n => n.id === edge.to);
                
                const fromSVG = normalizedToSVG(fromNode.x, fromNode.y);
                const toSVG = normalizedToSVG(toNode.x, toNode.y);
                
                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                const controlX = (fromSVG.x + toSVG.x) / 2;
                const controlY = (fromSVG.y + toSVG.y) / 2 - 40;
                const d = `M ${fromSVG.x} ${fromSVG.y} Q ${controlX} ${controlY} ${toSVG.x} ${toSVG.y}`;
                
                path.setAttribute('d', d);
                path.setAttribute('class', 'edge');
                path.setAttribute('data-from', edge.from);
                path.setAttribute('data-to', edge.to);
                svg.appendChild(path);
            });
        }

        // Create nodes
        function createNodes() {
            nodes.forEach(node => {
                const svgCoords = normalizedToSVG(node.x, node.y);
                const group = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                group.setAttribute('class', 'node');
                group.setAttribute('data-id', node.id);
                group.setAttribute('transform', `translate(${svgCoords.x}, ${svgCoords.y})`);

                let shape;
                if (node.shape === 'circle') {
                    shape = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                    shape.setAttribute('r', '40');
                    shape.setAttribute('fill', node.color);
                    shape.setAttribute('stroke', 'rgba(255,255,255,0.3)');
                    shape.setAttribute('stroke-width', '3');
                    shape.setAttribute('filter', 'drop-shadow(0 4px 8px rgba(0,0,0,0.3))');
                } else if (node.shape === 'rect') {
                    shape = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
                    shape.setAttribute('x', '-50');
                    shape.setAttribute('y', '-35');
                    shape.setAttribute('width', '100');
                    shape.setAttribute('height', '70');
                    shape.setAttribute('rx', '10');
                    shape.setAttribute('ry', '10');
                    shape.setAttribute('fill', node.color);
                    shape.setAttribute('stroke', 'rgba(255,255,255,0.3)');
                    shape.setAttribute('stroke-width', '3');
                    shape.setAttribute('filter', 'drop-shadow(0 4px 8px rgba(0,0,0,0.3))');
                } else if (node.shape === 'diamond') {
                    shape = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
                    shape.setAttribute('x', '-45');
                    shape.setAttribute('y', '-45');
                    shape.setAttribute('width', '90');
                    shape.setAttribute('height', '90');
                    shape.setAttribute('rx', '15');
                    shape.setAttribute('ry', '15');
                    shape.setAttribute('fill', node.color);
                    shape.setAttribute('stroke', 'rgba(255,255,255,0.3)');
                    shape.setAttribute('stroke-width', '3');
                    shape.setAttribute('transform', 'rotate(45)');
                    shape.setAttribute('filter', 'drop-shadow(0 4px 8px rgba(0,0,0,0.3))');
                }

                const iconText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                iconText.setAttribute('class', 'node-icon');
                iconText.setAttribute('y', '-8');
                iconText.textContent = node.icon;

                const labelText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                labelText.setAttribute('class', 'node-text');
                labelText.setAttribute('y', '8');
                labelText.textContent = node.label;

                group.appendChild(shape);
                group.appendChild(iconText);
                group.appendChild(labelText);

                // Add event listeners
                group.addEventListener('mouseenter', (e) => {
                    showTooltip(e, node.id);
                    // Scale up the node using SVG transform
                    const currentTransform = group.getAttribute('transform');
                    if (!currentTransform.includes('scale')) {
                        group.setAttribute('transform', currentTransform + ' scale(1.1)');
                    }
                });
                group.addEventListener('mouseleave', (e) => {
                    hideTooltip();
                    // Reset scale by removing it from transform
                    const currentTransform = group.getAttribute('transform');
                    const newTransform = currentTransform.replace(/ scale\([^)]*\)/, '');
                    group.setAttribute('transform', newTransform);
                });
                group.addEventListener('click', (e) => {
                    e.stopPropagation();
                    openModal(node.id);
                });
                group.addEventListener('mousedown', (e) => startDrag(e, node));

                svg.appendChild(group);
            });
        }

        // Tooltip functions - updated to show current inference results
        function showTooltip(event, nodeId) {
            let tableHtml = '';
            
            if (inferenceResults[nodeId]) {
                const result = inferenceResults[nodeId];
                tableHtml = `
                    <table class="cpd-table">
                        <thead>
                            <tr><th>State</th><th>Current Probability</th></tr>
                        </thead>
                        <tbody>
                            ${result.states.map((state, index) => 
                                `<tr><td>${nodeId} = ${state}</td><td>${(result.probabilities[index] * 100).toFixed(1)}%</td></tr>`
                            ).join('')}
                        </tbody>
                    </table>
                `;
            } else {
                tableHtml = '<p>Loading probabilities...</p>';
            }

            tooltip.innerHTML = `
                <h3>📊 Current Probability Distribution</h3>
                <h4>${nodeId}</h4>
                <div style="overflow-x: auto; max-width: 100%;">
                    ${tableHtml}
                </div>
            `;

            tooltip.classList.add('show');
            positionTooltip(event);
        }

        function hideTooltip() {
            tooltip.classList.remove('show');
        }

        function positionTooltip(event) {
            const containerRect = document.querySelector('.network-container').getBoundingClientRect();
            const svgRect = svg.getBoundingClientRect();
            
            // Get mouse position relative to container
            const mouseX = event.clientX - containerRect.left;
            const mouseY = event.clientY - containerRect.top;
            
            // Calculate tooltip position with overflow prevention
            let tooltipX = mouseX + 20;
            let tooltipY = mouseY - tooltip.offsetHeight - 10;
            
            // Prevent horizontal overflow
            if (tooltipX + tooltip.offsetWidth > containerRect.width) {
                tooltipX = mouseX - tooltip.offsetWidth - 20;
            }
            
            // Prevent vertical overflow
            if (tooltipY < 0) {
                tooltipY = mouseY + 20;
            }
            
            // Ensure tooltip stays within container bounds
            tooltipX = Math.max(10, Math.min(tooltipX, containerRect.width - tooltip.offsetWidth - 10));
            tooltipY = Math.max(10, Math.min(tooltipY, containerRect.height - tooltip.offsetHeight - 10));
            
            tooltip.style.left = tooltipX + 'px';
            tooltip.style.top = tooltipY + 'px';
        }

        // Drag functionality
        function startDrag(event, node) {
            isDragging = true;
            currentNode = node;
            event.preventDefault();
        }

        svg.addEventListener('mousemove', (event) => {
            if (isDragging && currentNode) {
                const svgRect = svg.getBoundingClientRect();
                const svgX = ((event.clientX - svgRect.left) / svgRect.width) * 800;
                const svgY = ((event.clientY - svgRect.top) / svgRect.height) * 600;
                
                // Update normalized coordinates
                currentNode.x = svgX / 800;
                currentNode.y = svgY / 600;
                
                // Update node position
                const nodeElement = svg.querySelector(`[data-id="${currentNode.id}"]`);
                nodeElement.setAttribute('transform', `translate(${svgX}, ${svgY})`);
                // Clear any CSS transform when dragging to avoid conflicts
                nodeElement.style.transform = '';
                
                updateEdges();
            }
        });

        svg.addEventListener('mouseup', () => {
            isDragging = false;
            currentNode = null;
        });

        svg.addEventListener('mouseleave', () => {
            isDragging = false;
            currentNode = null;
        });

        function updateEdges() {
            edges.forEach(edge => {
                const fromNode = nodes.find(n => n.id === edge.from);
                const toNode = nodes.find(n => n.id === edge.to);
                const edgeElement = svg.querySelector(`[data-from="${edge.from}"][data-to="${edge.to}"]`);
                
                if (edgeElement && fromNode && toNode) {
                    const fromSVG = normalizedToSVG(fromNode.x, fromNode.y);
                    const toSVG = normalizedToSVG(toNode.x, toNode.y);
                    const controlX = (fromSVG.x + toSVG.x) / 2;
                    const controlY = (fromSVG.y + toSVG.y) / 2 - 40;
                    const d = `M ${fromSVG.x} ${fromSVG.y} Q ${controlX} ${controlY} ${toSVG.x} ${toSVG.y}`;
                    edgeElement.setAttribute('d', d);
                }
            });
        }

        // Modal functions - updated to show current inference results
        function openModal(nodeId) {
            const modal = document.getElementById('probabilityModal');
            const modalTitle = document.getElementById('modalTitle');
            const modalTableContainer = document.getElementById('modalTableContainer');
            
            const nodeInfo = nodes.find(n => n.id === nodeId);
            modalTitle.textContent = `${nodeInfo.icon} ${nodeInfo.label} - Current Probability Distribution`;
            
            let tableHtml = '';
            if (inferenceResults[nodeId]) {
                const result = inferenceResults[nodeId];
                tableHtml = `
                    <table class="modal-table">
                        <thead>
                            <tr><th>State</th><th>Current Probability</th><th>Percentage</th></tr>
                        </thead>
                        <tbody>
                            ${result.states.map((state, index) => 
                                `<tr>
                                    <td>${nodeId} = ${state}</td>
                                    <td>${result.probabilities[index].toFixed(4)}</td>
                                    <td>${(result.probabilities[index] * 100).toFixed(1)}%</td>
                                </tr>`
                            ).join('')}
                        </tbody>
                    </table>
                `;
            } else {
                tableHtml = '<p>Loading probabilities...</p>';
            }
            
            modalTableContainer.innerHTML = tableHtml;
            modal.classList.add('show');
            
            // Prevent body scroll when modal is open
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            const modal = document.getElementById('probabilityModal');
            modal.classList.remove('show');
            
            // Restore body scroll
            document.body.style.overflow = '';
        }

        // Close modal when clicking outside of it
        window.addEventListener('click', (event) => {
            const modal = document.getElementById('probabilityModal');
            if (event.target === modal) {
                closeModal();
            }
        });

        // Close modal with Escape key
        window.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                closeModal();
            }
        });

        // Initialize the visualization
        async function initialize() {
            createEdges();
            createNodes();
            await checkServerConnection();
        }

        // Start the application
        initialize();
    </script>
</body>
</html>
