<html>
    <head>
        <meta charset="utf-8">
        
            <script src="lib/bindings/utils.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css" integrity="sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
            <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
            
        
<center>
<h1></h1>
</center>

<!-- <link rel="stylesheet" href="../node_modules/vis/dist/vis.min.css" type="text/css" />
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>-->
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/css/bootstrap.min.css"
          rel="stylesheet"
          integrity="sha384-eOJMYsd53ii+scO/bJGFsiCZc+5NDVN2yr8+0RDqr0Ql0h+rP48ckxlpbzKgwra6"
          crossorigin="anonymous"
        />
        <script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>


        <center>
          <h1></h1>
        </center>
        <style type="text/css">

             #mynetwork {
                 width: 100%;
                 height: 750px;
                 background-color: #222222;
                 border: 1px solid lightgray;
                 position: relative;
                 float: left;
             }

             

             

             
        </style>
    </head>


    <body>
        <div class="card" style="width: 100%">
            
            
            <div id="mynetwork" class="card-body"></div>
        </div>

        
        

        <script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "#FFC300", "font": {"size": 20}, "id": "Alcohol_Abuse", "label": "Alcohol_Abuse", "shape": "ellipse", "size": 40}, {"color": "#DAF7A6", "font": {"size": 20}, "id": "AST_ALT_Ratio", "label": "AST_ALT_Ratio", "shape": "box", "size": 40}, {"color": "#DAF7A6", "font": {"size": 20}, "id": "GGT", "label": "GGT", "shape": "box", "size": 40}, {"color": "#FF5733", "font": {"size": 20}, "id": "Diagnosis", "label": "Diagnosis", "shape": "diamond", "size": 60}, {"color": "#FFC300", "font": {"size": 20}, "id": "Liver_Disease", "label": "Liver_Disease", "shape": "ellipse", "size": 40}]);
                  edges = new vis.DataSet([{"arrows": "to", "from": "Alcohol_Abuse", "to": "AST_ALT_Ratio", "width": 1}, {"arrows": "to", "from": "Alcohol_Abuse", "to": "GGT", "width": 1}, {"arrows": "to", "from": "AST_ALT_Ratio", "to": "Diagnosis", "width": 1}, {"arrows": "to", "from": "GGT", "to": "Diagnosis", "width": 1}, {"arrows": "to", "from": "Liver_Disease", "to": "AST_ALT_Ratio", "width": 1}, {"arrows": "to", "from": "Liver_Disease", "to": "GGT", "width": 1}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"physics": {"barnesHut": {"gravitationalConstant": -30000, "centralGravity": 0.3, "springLength": 200, "springConstant": 0.05}, "stabilization": {"iterations": 2500}}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
    </body>
</html>