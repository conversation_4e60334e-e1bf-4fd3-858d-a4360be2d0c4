{"level":"info","message":"Starting MCP server","service":"mcp-puppeteer","timestamp":"2025-07-09 19:49:24.525"}
{"level":"info","message":"MCP server started successfully","service":"mcp-puppeteer","timestamp":"2025-07-09 19:49:24.527"}
{"level":"info","message":"Puppeteer MCP Server closing","service":"mcp-puppeteer","timestamp":"2025-07-09 20:06:53.205"}
{"level":"info","message":"Starting MCP server","service":"mcp-puppeteer","timestamp":"2025-07-09 20:07:37.489"}
{"level":"info","message":"MCP server started successfully","service":"mcp-puppeteer","timestamp":"2025-07-09 20:07:37.491"}
{"level":"info","message":"Puppeteer MCP Server closing","service":"mcp-puppeteer","timestamp":"2025-07-09 20:09:39.929"}
{"level":"info","message":"Starting MCP server","service":"mcp-puppeteer","timestamp":"2025-07-09 20:10:49.844"}
{"level":"info","message":"MCP server started successfully","service":"mcp-puppeteer","timestamp":"2025-07-09 20:10:49.846"}
{"level":"info","message":"Puppeteer MCP Server closing","service":"mcp-puppeteer","timestamp":"2025-07-09 20:18:46.282"}
{"level":"info","message":"Starting MCP server","service":"mcp-puppeteer","timestamp":"2025-07-09 20:18:53.454"}
{"level":"info","message":"MCP server started successfully","service":"mcp-puppeteer","timestamp":"2025-07-09 20:18:53.456"}
{"level":"info","message":"Puppeteer MCP Server closing","service":"mcp-puppeteer","timestamp":"2025-07-09 22:33:42.791"}
