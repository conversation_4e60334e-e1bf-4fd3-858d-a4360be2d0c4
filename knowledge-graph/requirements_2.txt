aiohappyeyeballs==2.6.1
aiohttp==3.12.13
aiosignal==1.3.2
alembic==1.16.2
annotated-types==0.7.0
anthropic==0.3.4
anyio==3.7.1
appdirs==1.4.4
apturl==0.5.2
argcomplete==1.8.1
asttokens==3.0.0
async-timeout==5.0.1
attrs==25.3.0
Automat==20.2.0
Babel==2.8.0
backoff==2.2.1
bcrypt==3.2.0
blinker==1.9.0
blis==0.7.11
Brlapi==0.8.3
cachetools==5.5.2
catalogue==2.0.10
celluloid==0.2.0
certifi==2025.6.15
cffi==1.17.1
chardet==4.0.0
charset-normalizer==3.4.2
class-resolver==0.6.0
click==8.2.1
click-default-group==1.2.4
cloudpathlib==0.21.1
cloudpickle==3.1.1
cohere==4.13.1
colorama==0.4.4
colorlog==6.9.0
command-not-found==0.3
conan==2.17.0
confection==0.1.5
configobj==5.0.6
conllu==6.0.0
constantly==15.1.0
contourpy==1.3.2
cryptography==45.0.4
ctakesclient==5.1.0
cupshelpers==1.0
cycler==0.12.1
cymem==2.0.11
Cython==0.29.28
databricks-sdk==0.57.0
dataclasses-json==0.6.7
datasets==3.6.0
dbus-python==1.2.18
decorator==5.2.1
defer==1.0.6
dill==0.3.8
distlib==0.3.9
distro==1.7.0
distro-info==1.1+ubuntu0.2
docdata==0.0.5
docker==7.1.0
duplicity==0.8.21
en_core_sci_scibert @ https://s3-us-west-2.amazonaws.com/ai2-s2-scispacy/releases/v0.5.4/en_core_sci_scibert-0.5.4.tar.gz#sha256=be4afaa2710d63206fafa7864001acbfab1225800bbab577f32765435e29b7f2
exceptiongroup==1.3.0
executing==2.2.0
fastapi==0.115.14
fastcoref==2.1.6
fasteners==0.19
fastjsonschema==2.21.1
fhirclient==4.3.1
filelock==3.18.0
Flask==3.1.1
flit_core==3.12.0
fonttools==4.58.4
frozenlist==1.7.0
fsspec==2025.3.0
future==0.18.2
gitdb==4.0.12
GitPython==3.1.44
google-auth==2.40.3
graphene==3.4.3
graphql-core==3.2.6
graphql-relay==3.2.0
greenlet==3.2.3
gunicorn==23.0.0
gyp==0.1
h11==0.16.0
hf-xet==1.1.3
httpcore==1.0.9
httplib2==0.20.2
httpx==0.28.1
huggingface-hub==0.33.1
hyperlink==21.0.0
hypernetx==2.2.0
hyperon==0.2.5
idna==3.10
igraph==0.11.9
importlib-metadata==6.11.0
importlib_resources==6.5.2
incremental==21.3.0
iniconfig==2.1.0
ipython==8.37.0
isodate==0.6.1
itsdangerous==2.2.0
jedi==0.19.2
jeepney==0.7.1
Jinja2==2.11.3
jiter==0.10.0
joblib==1.5.1
json5==0.12.0
jsonpickle==4.1.1
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
keyring==23.5.0
kiwisolver==1.4.8
langcodes==3.5.0
language-selector==0.1
language_data==1.3.0
launchpadlib==1.10.16
lazr.restfulclient==0.14.4
lazr.uri==1.0.6
livereload==2.6.3
lockfile==0.12.2
louis==3.20.0
macaroonbakery==1.3.1
Mako==1.1.3
marisa-trie==1.2.1
Markdown==3.3.6
markdown-it-py==3.0.0
MarkupSafe==2.0.1
marshmallow==3.26.1
matplotlib==3.10.3
matplotlib-inline==0.1.7
mdurl==0.1.2
medspacy==1.3.1
medspacy_quickumls==3.2
medspacy_unqlite==0.9.8
mkdocs==1.1.2
mlflow==3.1.1
mlflow-skinny==3.1.1
monotonic==1.6
more-click==0.1.2
more-itertools==8.10.0
mpmath==1.3.0
multidict==6.5.0
multiprocess==0.70.16
murmurhash==1.0.13
mypy_extensions==1.1.0
netifaces==0.11.0
networkx==2.8.8
nltk==3.9.1
nmslib==2.1.1
nose==1.3.7
nose3==1.3.8
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-cufile-cu12==********
nvidia-curand-cu12==*********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==********
nvidia-cusparselt-cu12==0.6.3
nvidia-ml-py==12.575.51
nvidia-nccl-cu12==2.26.2
nvidia-nvjitlink-cu12==12.6.85
nvidia-nvtx-cu12==12.6.77
oauthlib==3.2.0
olefile==0.46
openai==0.27.0
opentelemetry-api==1.34.1
opentelemetry-sdk==1.34.1
opentelemetry-semantic-conventions==0.55b1
optuna==4.4.0
packaging==25.0
pandas==2.3.0
paramiko==2.9.3
parso==0.8.4
patch-ng==1.18.1
pdfminer.six==20250506
pexpect==4.9.0
Pillow==9.0.1
pipx==1.0.0
platformdirs==4.3.8
pluggy==1.6.0
ply==3.11
preshed==3.0.10
prompt_toolkit==3.0.51
prompter==0.3.10
propcache==0.3.2
protobuf==3.12.4
psutil==7.0.0
ptyprocess==0.7.0
pure_eval==0.2.3
pyarrow==20.0.0
pyasn1==0.4.8
pyasn1-modules==0.2.1
pybind11==2.6.1
pycairo==1.20.1
pycparser==2.22
pycups==2.0.1
pycurl==7.44.1
pydantic==2.11.7
pydantic_core==2.33.2
PyFastNER==1.0.10
Pygments==2.19.1
PyGObject==3.42.1
PyHamcrest==2.0.2
pyinotify==0.9.6
PyJWT==2.3.0
pykeen @ git+https://github.com/pykeen/pykeen.git@42bfa99a17ee489d0d54dc6332d5cc71fc0ded08
pymacaroons==0.13.0
PyNaCl==1.5.0
pynvml==12.0.0
pyOpenSSL==21.0.0
pyparsing==2.4.7
pyRFC3339==1.1
PyRuSH==1.0.9
pysbd==0.3.4
pysimstring==1.3.0
pystow==0.7.1
pytest==7.2.2
python-apt==2.4.0+ubuntu4
python-dateutil==2.9.0.post0
python-debian==0.1.43+ubuntu1.1
python-dotenv==1.1.0
pytz==2022.1
pyvis==0.3.2
pyxdg==0.27
PyYAML==6.0.2
quicksectx==0.4.0
rdflib==7.0.0
referencing==0.36.2
regex==2022.10.31
reportlab==3.6.8
requests==2.32.4
rich==14.0.0
rpds-py==0.25.1
rsa==4.9.1
safetensors==0.5.3
scikit-learn==1.7.0
scipy==1.15.3
scispacy==0.5.5
screen-resolution-extra==0.0.0
SecretStorage==3.3.1
semantic-chunker==0.2.0
semantic-text-splitter==0.27.0
service-identity==18.1.0
shellingham==1.5.4
six==1.16.0
smart-open==7.1.0
smmap==5.0.2
sniffio==1.3.1
spacy==3.7.5
spacy-alignments==0.9.2
spacy-legacy==3.0.12
spacy-loggers==1.0.5
spacy-transformers==1.3.9
SQLAlchemy==2.0.41
sqlparse==0.5.3
srsly==2.5.1
stack-data==0.6.3
starlette==0.46.2
swig==4.3.1
sympy==1.14.0
systemd-python==234
tabulate==0.9.0
tenacity==8.2.2
texttable==1.7.0
thinc==8.2.5
threadpoolctl==3.6.0
tiktoken==0.4.0
tokenizers==0.13.3
tomli==2.2.1
torch==2.7.1
torch-max-mem==0.1.4
torch-ppr==0.0.8
tornado==6.1
tqdm==4.65.0
traitlets==5.14.3
transformers==4.25.1
tree-sitter==0.24.0
tree-sitter-c-sharp==0.23.1
tree-sitter-embedded-template==0.23.2
tree-sitter-language-pack==0.8.0
tree-sitter-yaml==0.7.1
triton==3.3.1
Twisted==22.1.0
typer==0.16.0
typing-inspect==0.9.0
typing-inspection==0.4.1
typing_extensions==4.14.0
tzdata==2025.2
ubuntu-drivers-common==0.0.0
ubuntu-pro-client==8001
ufw==0.36.1
unattended-upgrades==0.1
Unidecode==1.4.0
urllib3==2.4.0
usb-creator==0.3.7
userpath==1.8.0
uv==0.7.13
uvicorn==0.35.0
virtualenv==20.31.2
wadllib==1.3.6
wasabi==1.1.3
wcwidth==0.2.13
weasel==0.4.1
webanno_tsv @ git+https://github.com/neuged/webanno_tsv@717fcc8fb94819be790df0aca1fc8aef7fdf9edb
Werkzeug==3.1.3
wrapt==1.17.2
xdg==5
xkit==0.0.0
xxhash==3.5.0
yarl==1.20.1
zipp==3.23.0
zope.interface==5.4.0
