```json
{
  "graph_type": "N-tuple Hyper-Relational Temporal Knowledge Graph",
  "metadata": {
    "sentence_id": "SENT_4",
    "source_sentence": "Understanding Cholestasis: Mechanical or Medical? Mechanical biliary obstruction results in raised levels of ALP,GGT and often bilirubin. ALP will usually be markedly raised in comparison with ALT. Levels of ALP and GGT elevated in similar proportions signify a hepatobiliary source. Otherwise alternative causes of single enzyme elevation should be considered. When due to choledocholithiasis, the levels of ALP and GGT tend to fluctuate (in comparison to stricture forming disease) and may be associated with a normal bilirubin. Enzyme titres tend to rise and fall gradually and may be preceded by a peaked rise in liver transaminases which can reach >1000 I/U."
  },
  "entities": [
    { "id": "E1", "name": "Mechanical biliary obstruction", "type": "Disease" },
    { "id": "E2", "name": "ALP", "type": "Biomarker" },
    { "id": "E3", "name": "GGT", "type": "Biomarker" },
    { "id": "E4", "name": "Bilirubin", "type": "Biomarker" },
    { "id": "E5", "name": "ALT", "type": "Biomarker" },
    { "id": "E6", "name": "Hepatobiliary source", "type": "Disease" },
    { "id": "E7", "name": "Single enzyme elevation", "type": "Concept" },
    { "id": "E8", "name": "Alternative causes", "type": "Concept" },
    { "id": "E9", "name": "Choledocholithiasis", "type": "Disease" },
    { "id": "E10", "name": "Stricture forming disease", "type": "Disease" },
    { "id": "E11", "name": "Liver transaminases", "type": "Biomarker" }
  ],
  "facts": [
    {
      "id": "F1",
      "predicate": "causes",
      "timestamp": "1900-01-01",
      "truth_value": [0.85, 0.70],
      "sentence_id": "SENT_4",
      "tuple": [
        { "role": "agent", "entity": "E1" },
        { "role": "effect", "entity": "E2" },
        { "role": "delta", "literal": "increased", "datatype": "qualitative" }
      ]
    },
    {
      "id": "F2",
      "predicate": "causes",
      "timestamp": "1900-01-01",
      "truth_value": [0.85, 0.70],
      "sentence_id": "SENT_4",
      "tuple": [
        { "role": "agent", "entity": "E1" },
        { "role": "effect", "entity": "E3" },
        { "role": "delta", "literal": "increased", "datatype": "qualitative" }
      ]
    },
    {
      "id": "F3",
      "predicate": "causes",
      "timestamp": "1900-01-01",
      "truth_value": [0.85, 0.70],
      "sentence_id": "SENT_4",
      "tuple": [
        { "role": "agent", "entity": "E1" },
        { "role": "effect", "entity": "E4" },
        { "role": "delta", "literal": "increased (often)", "datatype": "qualitative" }
      ]
    },
    {
      "id": "F4",
      "predicate": "observational",
      "timestamp": "1900-01-01",
      "truth_value": 0.90,
      "sentence_id": "SENT_4",
      "tuple": [
        { "role": "agent", "entity": "E2" },
        { "role": "comparison", "entity": "E5" },
        { "role": "delta", "literal": "markedly raised", "datatype": "qualitative" }
      ]
    },
    {
      "id": "F5",
      "predicate": "observational",
      "timestamp": "1900-01-01",
      "truth_value": 0.90,
      "sentence_id": "SENT_4",
      "tuple": [
        { "role": "agent", "entity": "E2" },
        { "role": "co_agent", "entity": "E3" },
        { "role": "delta", "literal": "elevated in similar proportions", "datatype": "qualitative" }
      ]
    },
    {
      "id": "F6",
      "predicate": "causes",
      "timestamp": "1900-01-01",
      "truth_value": [0.85, 0.70],
      "sentence_id": "SENT_4",
      "tuple": [
        { "role": "agent", "entity": "E2" },
        { "role": "co_agent", "entity": "E3" },
        { "role": "effect", "entity": "E6" }
      ]
    },
    {
      "id": "F7",
      "predicate": "requires_investigation",
      "timestamp": "1900-01-01",
      "truth_value": 0.50,
      "sentence_id": "SENT_4",
      "tuple": [
        { "role": "target", "entity": "E7" },
        { "role": "action", "literal": "consider alternative causes", "datatype": "string" }
      ]
    },
    {
      "id": "F8",
      "predicate": "causes",
      "timestamp": "1900-01-01",
      "truth_value": [0.85, 0.70],
      "sentence_id": "SENT_4",
      "tuple": [
        { "role": "agent", "entity": "E9" },
        { "role": "effect", "entity": "E2" },
        { "role": "co_effect", "entity": "E3" },
        { "role": "delta", "literal": "fluctuate", "datatype": "qualitative" }
      ]
    },
    {
      "id": "F9",
      "predicate": "observational",
      "timestamp": "1900-01-01",
      "truth_value": 0.90,
      "sentence_id": "SENT_4",
      "tuple": [
        { "role": "agent", "entity": "E9" },
        { "role": "effect", "entity": "E4" },
        { "role": "delta", "literal": "may be normal", "datatype": "qualitative" }
      ]
    },
    {
      "id": "F10",
      "predicate": "observational",
      "timestamp": "1900-01-01",
      "truth_value": 0.90,
      "sentence_id": "SENT_4",
      "tuple": [
        { "role": "agent", "entity": "E2" },
        { "role": "delta", "literal": "rise and fall gradually", "datatype": "qualitative" }
      ]
    },
    {
      "id": "F11",
      "predicate": "observational",
      "timestamp": "1900-01-01",
      "truth_value": 0.90,
      "sentence_id": "SENT_4",
      "tuple": [
        { "role": "agent", "entity": "E11" },
        { "role": "delta", "literal": "peaked rise >1000 I/U", "datatype": "quantitative" }
      ]
    }
  ]
}
```