```json
{
  "graph_type": "N-tuple Hyper-Relational Temporal Knowledge Graph",
  "metadata": {
    "sentence_id": "SENT_15",
    "source_sentence": "A scoring system developed at the Mayo clinic uses age, hyperglycemia, body mass index, platelet count, albumin, and AST:ALT ratio to accurately differentiate patients with advanced fibrosis in NAFLD. the AST:ALT Ratio becomes considerably less specific in determining underlying disease with the development of cirrhosis, as the AST:ALT Ratio will increase across a broad range of diseases. the AST:ALT Ratio is, however, useful in NAFLD patients known not to be abusing alcohol as a score of >1 should lead to the consideration that NAFLD patients known not to be abusing alcohol may have developed cirrhosis."
  },
  "entities": [
    { "id": "E1", "name": "Scoring system", "type": "ClinicalTool" },
    { "id": "E2", "name": "Mayo clinic", "type": "Institution" },
    { "id": "E3", "name": "Age", "type": "Biomarker" },
    { "id": "E4", "name": "Hyperglycemia", "type": "Biomarker" },
    { "id": "E5", "name": "Body mass index", "type": "Biomarker" },
    { "id": "E6", "name": "Platelet count", "type": "Biomarker" },
    { "id": "E7", "name": "Albumin", "type": "Biomarker" },
    { "id": "E8", "name": "AST:ALT ratio", "type": "Biomarker" },
    { "id": "E9", "name": "Advanced fibrosis", "type": "Disease" },
    { "id": "E10", "name": "NAFLD", "type": "Disease" },
    { "id": "E11", "name": "Cirrhosis", "type": "Disease" },
    { "id": "E12", "name": "Underlying disease", "type": "Disease" },
    { "id": "E13", "name": "Alcohol abuse", "type": "Condition" },
    { "id": "E14", "name": "Score >1", "type": "Threshold" }
  ],
  "facts": [
    {
      "id": "F1",
      "predicate": "differentiates",
      "timestamp": "1900-01-01",
      "truth_value": 0.90,
      "sentence_id": "SENT_15",
      "tuple": [
        { "role": "agent", "entity": "E1" },
        { "role": "target", "entity": "E9" },
        { "role": "condition", "entity": "E10" },
        { "role": "component", "entity": "E3" },
        { "role": "component", "entity": "E4" },
        { "role": "component", "entity": "E5" },
        { "role": "component", "entity": "E6" },
        { "role": "component", "entity": "E7" },
        { "role": "component", "entity": "E8" }
      ]
    },
    {
      "id": "F2",
      "predicate": "specificity_decreases",
      "timestamp": "1900-01-01",
      "truth_value": 0.90,
      "sentence_id": "SENT_15",
      "tuple": [
        { "role": "agent", "entity": "E8" },
        { "role": "target", "entity": "E12" },
        { "role": "condition", "entity": "E11" }
      ]
    },
    {
      "id": "F3",
      "predicate": "level_increases",
      "timestamp": "1900-01-01",
      "truth_value": 0.90,
      "sentence_id": "SENT_15",
      "tuple": [
        { "role": "agent", "entity": "E8" },
        { "role": "target", "entity": "E12" }
      ]
    },
    {
      "id": "F4",
      "predicate": "useful_for",
      "timestamp": "1900-01-01",
      "truth_value": 0.90,
      "sentence_id": "SENT_15",
      "tuple": [
        { "role": "agent", "entity": "E8" },
        { "role": "target", "entity": "E10" },
        { "role": "condition", "entity": "E13" }
      ]
    },
    {
      "id": "F5",
      "predicate": "requires_investigation",
      "timestamp": "1900-01-01",
      "truth_value": 0.60,
      "sentence_id": "SENT_15",
      "tuple": [
        { "role": "target", "entity": "E10" },
        { "role": "condition", "entity": "E13" },
        { "role": "threshold", "entity": "E14" },
        { "role": "action", "literal": "consider cirrhosis", "datatype": "string" }
      ]
    },
    {
      "id": "F6",
      "predicate": "implies",
      "timestamp": "1900-01-01",
      "truth_value": [0.85, 0.70],
      "sentence_id": "SENT_15",
      "arguments": ["F4", "F5"]
    }
  ]
}
```