```json
{
  "graph_type": "N-tuple Hyper-Relational Temporal Knowledge Graph",
  "metadata": {
    "sentence_id": "SENT_2",
    "source_sentence": "Whether for the investigation of suspected liver disease, monitoring of disease activity, or simply as ‘routine’ blood analysis, Liver Function Tests (LFTs) can provide a host of information on a range of disease processes. The title ‘liver function tests’ is, however, somewhat of a misnomer; only the bilirubin and albumin given in this panel offer information regarding the functional capacity of the liver. At a basic level the evaluation of liver enzymes simply gives information as to whether a patient’s primary disorder is hepatitic or cholestatic in origin. However, much more may be interpreted from these assays with knowledge of enzyme ratios and pattern recognition."
  },
  "entities": [
    { "id": "E1", "name": "Liver Function Tests", "type": "ClinicalAction" },
    { "id": "E2", "name": "Liver disease", "type": "Disease" },
    { "id": "E3", "name": "Disease activity", "type": "Endpoint" },
    { "id": "E4", "name": "Blood analysis", "type": "ClinicalAction" },
    { "id": "E5", "name": "Bilirubin", "type": "Biomarker" },
    { "id": "E6", "name": "Albumin", "type": "Biomarker" },
    { "id": "E7", "name": "Functional capacity of the liver", "type": "Endpoint" },
    { "id": "E8", "name": "Liver enzymes", "type": "Biomarker" },
    { "id": "E9", "name": "Hepatitic disorder", "type": "Disease" },
    { "id": "E10", "name": "Cholestatic disorder", "type": "Disease" },
    { "id": "E11", "name": "Enzyme ratios", "type": "Biomarker" },
    { "id": "E12", "name": "Pattern recognition", "type": "Concept" }
  ],
  "facts": [
    {
      "id": "F1",
      "predicate": "provides_information_on",
      "timestamp": "1900-01-01",
      "truth_value": 0.90,
      "sentence_id": "SENT_2",
      "tuple": [
        { "role": "agent", "entity": "E1" },
        { "role": "target", "entity": "E2" }
      ]
    },
    {
      "id": "F2",
      "predicate": "provides_information_on",
      "timestamp": "1900-01-01",
      "truth_value": 0.90,
      "sentence_id": "SENT_2",
      "tuple": [
        { "role": "agent", "entity": "E1" },
        { "role": "target", "entity": "E3" }
      ]
    },
    {
      "id": "F3",
      "predicate": "provides_information_on",
      "timestamp": "1900-01-01",
      "truth_value": 0.90,
      "sentence_id": "SENT_2",
      "tuple": [
        { "role": "agent", "entity": "E1" },
        { "role": "target", "entity": "E4" }
      ]
    },
    {
      "id": "F4",
      "predicate": "provides_information_on",
      "timestamp": "1900-01-01",
      "truth_value": 0.90,
      "sentence_id": "SENT_2",
      "tuple": [
        { "role": "agent", "entity": "E5" },
        { "role": "target", "entity": "E7" }
      ]
    },
    {
      "id": "F5",
      "predicate": "provides_information_on",
      "timestamp": "1900-01-01",
      "truth_value": 0.90,
      "sentence_id": "SENT_2",
      "tuple": [
        { "role": "agent", "entity": "E6" },
        { "role": "target", "entity": "E7" }
      ]
    },
    {
      "id": "F6",
      "predicate": "provides_information_on",
      "timestamp": "1900-01-01",
      "truth_value": 0.90,
      "sentence_id": "SENT_2",
      "tuple": [
        { "role": "agent", "entity": "E8" },
        { "role": "target", "entity": "E9" }
      ]
    },
    {
      "id": "F7",
      "predicate": "provides_information_on",
      "timestamp": "1900-01-01",
      "truth_value": 0.90,
      "sentence_id": "SENT_2",
      "tuple": [
        { "role": "agent", "entity": "E8" },
        { "role": "target", "entity": "E10" }
      ]
    },
    {
      "id": "F8",
      "predicate": "requires_investigation",
      "timestamp": "1900-01-01",
      "truth_value": 0.60,
      "sentence_id": "SENT_2",
      "tuple": [
        { "role": "target", "entity": "E11" },
        { "role": "action", "entity": "E12" }
      ]
    },
    {
      "id": "F9",
      "predicate": "implies",
      "timestamp": "1900-01-01",
      "truth_value": [0.85, 0.70],
      "sentence_id": "SENT_2",
      "arguments": ["F6", "F7"]
    }
  ]
}
```