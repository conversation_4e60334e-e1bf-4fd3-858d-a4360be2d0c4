{"metadata": {"best_trial_evaluation": 0.20276039838790894, "best_trial_number": 12, "git_hash": "UNHASHED", "version": "1.11.2-dev"}, "pipeline": {"evaluator": "rankbased", "filter_validation_when_testing": true, "loss": "marginranking", "loss_kwargs": {"margin": 1.57977616618343}, "model": "transf", "model_kwargs": {"embedding_dim": 16, "random_seed": 42}, "negative_sampler": "basic", "negative_sampler_kwargs": {"num_negs_per_pos": 1}, "optimizer": "adam", "optimizer_kwargs": {"lr": 0.05177281797198914}, "testing": "<user defined>", "training": "<user defined>", "training_kwargs": {"batch_size": 128, "drop_last": false, "num_epochs": 25, "pin_memory": false}, "training_loop": "slcwa", "validation": "<user defined>"}}