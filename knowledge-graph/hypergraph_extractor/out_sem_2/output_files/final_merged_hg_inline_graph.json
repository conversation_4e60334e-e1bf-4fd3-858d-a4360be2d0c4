{"directed": true, "multigraph": true, "graph": {}, "nodes": [{"label": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "node_class": "Document", "color": "gold", "size": 25, "shape": "star", "id": "/home/<USER>/Downloads/hypergraph_extractor/input.txt"}, {"label": "Liver enzymes", "title": "Atom: Liver enzymes<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N1"}, {"label": "used_in_evaluation_of", "title": "Atom: used_in_evaluation_of<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N2"}, {"label": "Patients", "title": "Atom: Patients<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N3"}, {"label": "Diseases", "title": "Atom: Diseases<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N4"}, {"label": "gives_information_on", "title": "Atom: gives_information_on<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N5"}, {"label": "Primary disorder", "title": "Atom: Primary disorder<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N6"}, {"label": "Hepatitic origin", "title": "Atom: Hepatitic origin<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N7"}, {"label": "Cholestatic origin", "title": "Atom: Cholestatic origin<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N8"}, {"label": "Enzyme ratios", "title": "Atom: Enzyme ratios<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N9"}, {"label": "Pattern recognition", "title": "Atom: Pattern recognition<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N10"}, {"label": "derive_information_from", "title": "Atom: derive_information_from<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N11"}, {"label": "Liver disease", "title": "Atom: Liver disease<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N12"}, {"label": "Investigation", "title": "Atom: Investigation<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N13"}, {"label": "Management", "title": "Atom: Management<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N14"}, {"label": "improves", "title": "Atom: improves<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N15"}, {"label": "Liver Function Tests", "title": "Atom: Liver Function Tests<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N16"}, {"label": "used_as_screening", "title": "Atom: used_as_screening<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N17"}, {"label": "Blood tests", "title": "Atom: Blood tests<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N18"}, {"label": "SENT_1", "title": "Abstract Liver enzymes are commonly used in the evaluation of patients with a\nrange of diseases. Classically Liver enzymes are used to give information on\nwhether a patient’s primary disorder is hepatitic or cholestatic in origin.\nHowever, knowledge of enzyme ratios and pattern recognition allow much more\ninformation to be derived from Liver enzymes. This paper offers an insight to\ngeneralists on how to extract greater information from Liver enzymes in order to\nimprove the investigation and management of liver disease. Introduction Liver\nFunction Tests (LFTs) are one of the most commonlyrequested screening blood\ntests.", "node_class": "SentenceNode", "color": "lightgreen", "shape": "box", "size": 20, "id": "N19"}, {"label": "This paper", "title": "Atom: This paper<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N20"}, {"label": "Disease activity", "title": "Atom: Disease activity<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N22"}, {"label": "Blood analysis", "title": "Atom: Blood analysis<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N23"}, {"label": "Bilirubin", "title": "Atom: <PERSON><PERSON><PERSON><PERSON><br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N24"}, {"label": "Albumin", "title": "Atom: Albumin<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N25"}, {"label": "Liver functional capacity", "title": "Atom: Liver functional capacity<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N26"}, {"label": "Hepatitic disorder", "title": "Atom: Hepatitic disorder<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N27"}, {"label": "Cholestatic disorder", "title": "Atom: Cholestatic disorder<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N28"}, {"label": "provides information on", "title": "Atom: provides information on<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N29"}, {"label": "offers information on", "title": "Atom: offers information on<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N30"}, {"label": "evaluates", "title": "Atom: evaluates<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N31"}, {"label": "interpreted by", "title": "Atom: interpreted by<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N32"}, {"label": "origin", "title": "Atom: origin<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N33"}, {"label": "SENT_2", "title": "Whether for the investigation of suspected liver disease, monitoring of disease\nactivity, or simply as ‘routine’ blood analysis, Liver Function Tests (LFTs) can\nprovide a host of information on a range of disease processes. The title ‘liver\nfunction tests’ is, however, somewhat of a misnomer; only the bilirubin and\nalbumin given in this panel offer information regarding the functional capacity\nof the liver. At a basic level the evaluation of liver enzymes simply gives\ninformation as to whether a patient’s primary disorder is hepatitic or\ncholestatic in origin. However, much more may be interpreted from these assays\nwith knowledge of enzyme ratios and pattern recognition.", "node_class": "SentenceNode", "color": "lightgreen", "shape": "box", "size": 20, "id": "N34"}, {"label": "SENT_3", "title": "This paper offers an insight to generalists of how to yield greater information\nfrom this simple test. Patterns and Use of Hepatic Enzymes in Practice The liver\nenzyme profile should always be assessed in conjunction with a thorough history\nand clinical examination. Despite a thorough history and clinical examination,\nthere are many occasions when doubt persists over an underlying diagnosis. For\nexample, does an overweight diabetic who enjoys a few glasses of wine at the\nweekend have alcoholic or non-alcoholic fatty liver disease? In such\ncircumstances the absolute liver enzyme levels and ratios may point the\nclinician in the right direction. Furthermore, the pattern of enzymes will\nassist, not only with differentiating between cholestasis and hepatitis, but\nwill aid diagnosis when there is a mixed picture.", "node_class": "SentenceNode", "color": "lightgreen", "shape": "box", "size": 20, "id": "N35"}, {"label": "Hepatic enzymes", "title": "Atom: Hepatic enzymes<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N36"}, {"label": "Liver enzyme profile", "title": "Atom: Liver enzyme profile<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N37"}, {"label": "History and clinical examination", "title": "Atom: History and clinical examination<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N38"}, {"label": "Alcoholic fatty liver disease", "title": "Atom: Alcoholic fatty liver disease<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N39"}, {"label": "Non-alcoholic fatty liver disease", "title": "Atom: Non-alcoholic fatty liver disease<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N40"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "title": "Atom: <PERSON><PERSON><PERSON><PERSON><br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N41"}, {"label": "Hepatitis", "title": "Atom: Hepatitis<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N42"}, {"label": "Mixed liver disease picture", "title": "Atom: Mixed liver disease picture<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N43"}, {"label": "Diagnosis", "title": "Atom: Diagnosis<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N44"}, {"label": "differentiates", "title": "Atom: differentiates<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N45"}, {"label": "assists", "title": "Atom: assists<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N46"}, {"label": "aids", "title": "Atom: aids<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N47"}, {"label": "should_be_assessed_with", "title": "Atom: should_be_assessed_with<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N48"}, {"label": "SENT_4", "title": "Understanding Cholestasis: Mechanical or Medical? Mechanical biliary obstruction\nresults in raised levels of ALP,GGT and often bilirubin. ALP will usually be\nmarkedly raised in comparison with ALT. Levels of ALP and GGT elevated in\nsimilar proportions signify a hepatobiliary source. Otherwise alternative causes\nof single enzyme elevation should be considered. When due to\ncholedocholithiasis, the levels of ALP and GGT tend to fluctuate (in comparison\nto stricture forming disease) and may be associated with a normal bilirubin.\nEnzyme titres tend to rise and fall gradually and may be preceded by a peaked\nrise in liver transaminases which can reach >1000 I/U.", "node_class": "SentenceNode", "color": "lightgreen", "shape": "box", "size": 20, "id": "N49"}, {"label": "Mechanical biliary obstruction", "title": "Atom: Mechanical biliary obstruction<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N50"}, {"label": "Choledocholithiasis", "title": "Atom: Choledocholithiasis<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N51"}, {"label": "Stricture forming disease", "title": "Atom: Stricture forming disease<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N52"}, {"label": "Hepatobiliary source", "title": "Atom: Hepatobiliary source<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N53"}, {"label": "ALP", "title": "Atom: ALP<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N54"}, {"label": "GGT", "title": "Atom: GGT<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N55"}, {"label": "ALT", "title": "Atom: ALT<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N56"}, {"label": "Liver transaminases", "title": "Atom: Liver transaminases<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N57"}, {"label": "Enzyme titres", "title": "Atom: Enzyme titres<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N58"}, {"label": "increases", "title": "Atom: increases<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N59"}, {"label": "fluctuates", "title": "Atom: fluctuates<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N60"}, {"label": "signifies", "title": "Atom: signifies<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N61"}, {"label": "requires", "title": "Atom: requires<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N62"}, {"label": "precedes", "title": "Atom: precedes<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N63"}, {"label": "peaked_rise", "title": "Atom: peaked_rise<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N64"}, {"label": "SENT_5", "title": "The AST:ALT ratio (De Ritis ratio) may assist in differentiating the site of\nbiliary obstruction. When associated with a cholestatic picture, an AST:ALT\nratio of <1.5 suggests an extrahepatic obstruction. In such circumstances the\nALT titre is frequently considerably higher than AST. An AST:ALT ratio of >1.5\nindicates intrahepatic (mechanical or medical) cholestasis is more likely. Drug-\ninduced cholestasis usually presents with a preferential rise in ALP, rather\nthan GGT, or with an ALT:ALP ratio of <2. Causative drugs would include:\nantibiotics, immunosuppressants, tricyclic antidepressants and angiotensin\nconverting enzyme inhibitors.", "node_class": "SentenceNode", "color": "lightgreen", "shape": "box", "size": 20, "id": "N65"}, {"label": "AST:ALT ratio", "title": "Atom: AST:ALT ratio<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N66"}, {"label": "De Ritis ratio", "title": "Atom: De Ritis ratio<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N67"}, {"label": "Biliary obstruction", "title": "Atom: Biliary obstruction<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N68"}, {"label": "Cholestatic picture", "title": "Atom: Cholestatic picture<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N69"}, {"label": "Extrahepatic obstruction", "title": "Atom: Extrahepatic obstruction<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N70"}, {"label": "Intrahepatic cholestasis", "title": "Atom: Intrahepatic cholestasis<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N71"}, {"label": "Mechanical cholestasis", "title": "Atom: Mechanical cholestasis<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N72"}, {"label": "Medical cholestasis", "title": "Atom: Medical cholestasis<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N73"}, {"label": "AST", "title": "Atom: AST<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N74"}, {"label": "ALT:ALP ratio", "title": "Atom: ALT:ALP ratio<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N75"}, {"label": "Drug-induced cholestasis", "title": "Atom: Drug-induced cholestasis<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N76"}, {"label": "Antibiotics", "title": "Atom: Antibiotics<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N77"}, {"label": "Immunosuppressants", "title": "Atom: Immunosuppressants<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N78"}, {"label": "Tricyclic antidepressants", "title": "Atom: Tricyclic antidepressants<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N79"}, {"label": "Angiotensin converting enzyme inhibitors", "title": "Atom: Angiotensin converting enzyme inhibitors<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N80"}, {"label": "suggests", "title": "Atom: suggests<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N81"}, {"label": "indicates", "title": "Atom: indicates<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N82"}, {"label": "higher_than", "title": "Atom: higher_than<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N83"}, {"label": "presents_with", "title": "Atom: presents_with<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N84"}, {"label": "causes", "title": "Atom: causes<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N85"}, {"label": "rise_in", "title": "Atom: rise_in<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N86"}, {"label": "<1.5", "title": "Atom: <1.5<br>Type: NumberNode", "node_class": "NumberNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N87"}, {"label": ">1.5", "title": "Atom: >1.5<br>Type: NumberNode", "node_class": "NumberNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N88"}, {"label": "<2", "title": "Atom: <2<br>Type: NumberNode", "node_class": "NumberNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N89"}, {"label": "Primary Biliary Cirrhosis", "title": "Atom: Primary Biliary Cirrhosis<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N90"}, {"label": "Autoimmune condition", "title": "Atom: Autoimmune condition<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N91"}, {"label": "Intrahepatic biliary ducts", "title": "Atom: Intrahepatic biliary ducts<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N92"}, {"label": "Alkaline phosphatase", "title": "Atom: Alkaline phosphatase<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N93"}, {"label": "Gamma-glutamyl transferase", "title": "Atom: Gamma-glutamyl transferase<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N94"}, {"label": "Transaminases", "title": "Atom: Transaminases<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N95"}, {"label": "Cholestatic liver enzyme levels", "title": "Atom: Cholestatic liver enzyme levels<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N96"}, {"label": "Antimitochondrial antibodies", "title": "Atom: Antimitochondrial antibodies<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N97"}, {"label": "Diagnosis of Primary Biliary Cirrhosis", "title": "Atom: Diagnosis of Primary Biliary Cirrhosis<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N98"}, {"label": "Imaging", "title": "Atom: Imaging<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N99"}, {"label": "Liver biopsy", "title": "Atom: Liver biopsy<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N100"}, {"label": "European Association for Study of the Liver", "title": "Atom: European Association for Study of the Liver<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N101"}, {"label": "American Association for Study of Liver Disease", "title": "Atom: American Association for Study of Liver Disease<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N102"}, {"label": "greater_than", "title": "Atom: greater_than<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N103"}, {"label": "normal", "title": "Atom: normal<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N104"}, {"label": "minimally_elevated", "title": "Atom: minimally_elevated<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N105"}, {"label": "recommend", "title": "Atom: recommend<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N106"}, {"label": "based_on", "title": "Atom: based_on<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N107"}, {"label": "conjunction_with", "title": "Atom: conjunction_with<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N108"}, {"label": "SENT_6", "title": "In Primary Biliary Cirrhosis, an autoimmune condition of the intrahepatic\nbiliary ducts, the level of ALP is generally greater than that of GGT. In this\ncase, transaminases are invariably normal or only minimally elevated. Both the\nEuropean Association for Study of the Liver (EASL) and the American Association\nfor Study of Liver Disease (AASLD) recommend that a diagnosis of PBC may be\nbased on cholestatic liver enzyme levels in conjunction with the demonstration\nof antimitochondrial antibodies. If either of these two criteria is absent,\nimaging and liver biopsy become necessary.", "node_class": "SentenceNode", "color": "lightgreen", "shape": "box", "size": 20, "id": "N109"}, {"label": "SENT_7", "title": "AST and ALP are used within some scoring criteria to monitor the effects of\nursodeoxycholic acid in the management of PBC. A recent study has shown that a\nraised AST:ALT ratio outperforms other non-histological indicators of cirrhosis\nin PBC, but still only achieves a low sensitivity and a specificity of 65-79%.\nAs with PBC, liver enzymes play a key role in the diagnosis of Primary\nSclerosing Cholangitis (PSC).", "node_class": "SentenceNode", "color": "lightgreen", "shape": "box", "size": 20, "id": "N110"}, {"label": "Ursodeoxycholic acid", "title": "Atom: Ursodeoxycholic acid<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N111"}, {"label": "Primary Biliary Cholangitis", "title": "Atom: Primary Biliary Cholangitis<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N112"}, {"label": "monitor", "title": "Atom: monitor<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N113"}, {"label": "management", "title": "Atom: management<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N114"}, {"label": "effects", "title": "Atom: effects<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N115"}, {"label": "scoring criteria", "title": "Atom: scoring criteria<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N116"}, {"label": "non-histological indicators of cirrhosis", "title": "Atom: non-histological indicators of cirrhosis<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N117"}, {"label": "Cirr<PERSON>is", "title": "Atom: Cirrhosis<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N118"}, {"label": "outperforms", "title": "Atom: outperforms<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N119"}, {"label": "sensitivity", "title": "Atom: sensitivity<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N120"}, {"label": "specificity", "title": "Atom: specificity<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N121"}, {"label": "low", "title": "Atom: low<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N122"}, {"label": "65-79%", "title": "Atom: 65-79%<br>Type: NumberNode", "node_class": "NumberNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N123"}, {"label": "liver enzymes", "title": "Atom: liver enzymes<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N124"}, {"label": "Primary Sclerosing Cholangitis", "title": "Atom: Primary Sclerosing Cholangitis<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N125"}, {"label": "diagnosis", "title": "Atom: diagnosis<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N126"}, {"label": "key role", "title": "Atom: key role<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N127"}, {"label": "2024-01-01", "title": "Atom: 2024-01-01<br>Type: TimestampNode", "node_class": "TimestampNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N128"}, {"label": "SENT_8", "title": "When other causes of liver disease have been excluded, a raised GGT, and\nparticularly ALP, are diagnostic when associated with typical Endoscopic\nRetrograde Cholangiopancreatography (ERCP) or Magnetic Resonance\nCholangiopancreatography (MRCP) findings. This can preclude the need for a liver\nbiopsy. Transaminase levels may be raised up to 2-3 times normal values in PSC\nbut this is not diagnostic. AST is a component of the Mayo Risk Score, which\ncalculates the risk of disease progression in PSC. A high Mayo Risk Score, and\nan AST:ALT ratio of >1.12 have been shown to be indicators of risk for the\ndevelopment of oesophageal varices.", "node_class": "SentenceNode", "color": "lightgreen", "shape": "box", "size": 20, "id": "N129"}, {"label": "Transaminase", "title": "Atom: Transaminase<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N130"}, {"label": "Mayo Risk Score", "title": "Atom: Mayo Risk Score<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N131"}, {"label": "Oesophageal varices", "title": "Atom: Oesophageal varices<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N132"}, {"label": "Endoscopic Retrograde Cholangiopancreatography", "title": "Atom: Endoscopic Retrograde Cholangiopancreatography<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N133"}, {"label": "Magnetic Resonance Cholangiopancreatography", "title": "Atom: Magnetic Resonance Cholangiopancreatography<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N134"}, {"label": "diagnostic", "title": "Atom: diagnostic<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N135"}, {"label": "precludes", "title": "Atom: precludes<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N136"}, {"label": "raised", "title": "Atom: raised<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N137"}, {"label": "component_of", "title": "Atom: component_of<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N138"}, {"label": "calculates", "title": "Atom: calculates<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N139"}, {"label": "indicator_of", "title": "Atom: indicator_of<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N140"}, {"label": "development_of", "title": "Atom: development_of<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N141"}, {"label": "Monitoring", "title": "Atom: Monitoring<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N142"}, {"label": "2-3x normal", "title": "Atom: 2-3x normal<br>Type: NumberNode", "node_class": "NumberNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N143"}, {"label": ">1.12", "title": "Atom: >1.12<br>Type: NumberNode", "node_class": "NumberNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N144"}, {"label": ">1", "title": "Atom: >1<br>Type: NumberNode", "node_class": "NumberNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N145"}, {"label": "Alcohol", "title": "Atom: Alcohol<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N146"}, {"label": "GGT:ALP ratio", "title": "Atom: GGT:ALP ratio<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N147"}, {"label": ">2.5", "title": "Atom: >2.5<br>Type: NumberNode", "node_class": "NumberNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N148"}, {"label": "Jaundice", "title": "Atom: <PERSON><PERSON><PERSON><PERSON><br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N149"}, {"label": "Macrocytosis", "title": "Atom: Macrocytosis<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N150"}, {"label": "Folate deficiency", "title": "Atom: Folate deficiency<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N151"}, {"label": "B12 deficiency", "title": "Atom: B12 deficiency<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N152"}, {"label": "Bone marrow suppression", "title": "Atom: Bone marrow suppression<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N153"}, {"label": "Alcoholic liver disease", "title": "Atom: Alcoholic liver disease<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N154"}, {"label": "supports", "title": "Atom: supports<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N155"}, {"label": "SENT_9", "title": "In Primary Sclerosing Cholangitis (PSC), as with other liver diseases, there are\nsuggestions that an AST:ALT ratio of >1 indicates the development of cirrhosis.\nAlcohol induces hepatic enzymes leading to a raised GGT with an ALP which may be\nnormal, or disproportionately lower than the GGT. A GGT:ALP ratio >2.5 in\nassociation with jaundice suggests alcohol as a cause of liver disease. The\npresence of a macrocytosis, due to either an associated dietary deficiency of\nfolate or B12, or due to a direct suppression of bone marrow by alcohol is\nsupportive of the diagnosis of alcoholic liver disease.", "node_class": "SentenceNode", "color": "lightgreen", "shape": "box", "size": 20, "id": "N156"}, {"label": "SENT_10", "title": "A raised GGT is not diagnostic of alcohol abuse, with research showing A raised\nGGT remains high in former drinkers as well as current drinkers. In men, the\nhighest levels of GGT occur in those who drink daily. In women, binge drinkers\nand those consuming alcohol without food will have especially high levels. The\nlevel of GGT is loosely dose dependant, with those in the top two quartiles of\nalcohol intake having the highest titres.", "node_class": "SentenceNode", "color": "lightgreen", "shape": "box", "size": 20, "id": "N157"}, {"label": "Alcohol abuse", "title": "Atom: Alcohol abuse<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N158"}, {"label": "Former drinkers", "title": "Atom: Former drinkers<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N159"}, {"label": "Current drinkers", "title": "Atom: Current drinkers<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N160"}, {"label": "Men", "title": "Atom: Men<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N161"}, {"label": "Daily drinkers", "title": "Atom: Daily drinkers<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N162"}, {"label": "Women", "title": "Atom: Women<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N163"}, {"label": "Binge drinkers", "title": "Atom: Binge drinkers<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N164"}, {"label": "Alcohol consumption without food", "title": "Atom: Alcohol consumption without food<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N165"}, {"label": "High GGT level", "title": "Atom: High GGT level<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N166"}, {"label": "Alcohol intake", "title": "Atom: Alcohol intake<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N167"}, {"label": "Top two quartiles of alcohol intake", "title": "Atom: Top two quartiles of alcohol intake<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N168"}, {"label": "Highest GGT level", "title": "Atom: Highest GGT level<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N169"}, {"label": "diagnostic_of", "title": "Atom: diagnostic_of<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N170"}, {"label": "remains_high_in", "title": "Atom: remains_high_in<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N171"}, {"label": "occurs_in", "title": "Atom: occurs_in<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N172"}, {"label": "dose_dependent", "title": "Atom: dose_dependent<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N173"}, {"label": "SENT_11", "title": "The Fatty Liver and the AST:ALT Ratio During the last few decades there has been\nresearch into using the AST:ALT ratio in the differentiation of alcoholic liver\ndisease (ALD) from other forms of liver disease, particularly the Non-alcoholic\nFatty Liver Disease (NAFLD) spectrum. Both AST and ALT enzymes require\npyridoxal-5’-phosphate (vitamin B6) to function properly. pyridoxal-5’-phosphate\n(vitamin B6)'s absence in nutritionally-deficient heavy-drinkers has a much\nlarger effect on the production of ALT than that of AST, causing the AST:ALT\nratio to rise. A normal AST:ALT ratio should be <1.", "node_class": "SentenceNode", "color": "lightgreen", "shape": "box", "size": 20, "id": "N174"}, {"label": "Alcoholic Liver Disease", "title": "Atom: Alcoholic Liver Disease<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N175"}, {"label": "Non-alcoholic Fatty Liver Disease", "title": "Atom: Non-alcoholic Fatty Liver Disease<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N176"}, {"label": "Liver Disease", "title": "Atom: Liver Disease<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N177"}, {"label": "pyridoxal-5’-phosphate (vitamin B6)", "title": "Atom: pyridoxal-5’-phosphate (vitamin B6)<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N178"}, {"label": "nutritionally-deficient heavy-drinker", "title": "Atom: nutritionally-deficient heavy-drinker<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N179"}, {"label": "ALT production", "title": "Atom: ALT production<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N180"}, {"label": "<1", "title": "Atom: <1<br>Type: NumberNode", "node_class": "NumberNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N181"}, {"label": "SENT_12", "title": "In patients with alcoholic liver disease, the AST:ALT ratio is >1 in 92% of\npatients, and >2 in 70%.13 AST:ALT scores >2 are, therefore, strongly suggestive\nof alcoholic liver disease and scores <1 more suggestive of the Non-alcoholic\nFatty Liver Disease (NAFLD) spectrum. High ratios reflect the severity of\nhepatitis or underlying liver disease rather than high alcohol consumption. This\nmeans that most heavy-drinkers will not have an AST: ALT ratio >1 as most heavy-\ndrinkers have not yet developed alcoholic liver disease (ALD).", "node_class": "SentenceNode", "color": "lightgreen", "shape": "box", "size": 20, "id": "N182"}, {"label": "Heavy drinker", "title": "Atom: Heavy drinker<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N183"}, {"label": ">2", "title": "Atom: >2<br>Type: NumberNode", "node_class": "NumberNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N184"}, {"label": "92%", "title": "Atom: 92%<br>Type: NumberNode", "node_class": "NumberNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N185"}, {"label": "70%", "title": "Atom: 70%<br>Type: NumberNode", "node_class": "NumberNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N186"}, {"label": "Severity", "title": "Atom: Severity<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N187"}, {"label": "High alcohol consumption", "title": "Atom: High alcohol consumption<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N188"}, {"label": "suggestive_of", "title": "Atom: suggestive_of<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N189"}, {"label": "reflects", "title": "Atom: reflects<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N190"}, {"label": "developed", "title": "Atom: developed<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N191"}, {"label": "less_than", "title": "Atom: less_than<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N192"}, {"label": "SENT_13", "title": "No studies have shown that the AST:ALT ratio, either alone or in combination\nwith other factors or models, has the necessary sensitivity or specificity to\ndefinitively differentiate between alcoholic liver disease (ALD) and NAFLD, but\nthe AST:ALT ratio acts as a useful clinical guide when considering the need for\nliver biopsy. It should also be noted that liver transaminases are known to\nworsen in response to cessation of alcohol intake (often coinciding with\nadmission to hospital) and that ALT has also been shown to rise simply from\nadmission to hospital, even in patients with no liver disease.", "node_class": "SentenceNode", "color": "lightgreen", "shape": "box", "size": 20, "id": "N193"}, {"label": "Sensitivity", "title": "Atom: Sensitivity<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N194"}, {"label": "Specificity", "title": "Atom: Specificity<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N195"}, {"label": "NAFLD", "title": "Atom: NAFLD<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N196"}, {"label": "Clinical guide", "title": "Atom: Clinical guide<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N197"}, {"label": "worsen", "title": "Atom: worsen<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N198"}, {"label": "Cessation of alcohol intake", "title": "Atom: Cessation of alcohol intake<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N199"}, {"label": "Admission to hospital", "title": "Atom: Admission to hospital<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N200"}, {"label": "rise", "title": "Atom: rise<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N201"}, {"label": "SENT_14", "title": "Although models exist which exclude cirrhosis in NAFLD with reasonable accuracy,\nliver enzyme analysis has so far failed to provide a sensitive and specific\nenough means to make a diagnosis. At present liver biopsy cannot be avoided in\ncases where confirmation of NASH or cirrhosis is necessary. The role of liver\nenzyme analysis in NAFLD lies in both the early identification and modification\nof associated metabolic risk factors such as hypertension, hyperlipidaemia and\nglycaemic control and in risk stratification for the future.", "node_class": "SentenceNode", "color": "lightgreen", "shape": "box", "size": 20, "id": "N202"}, {"label": "Non-alcoholic steatohepatitis", "title": "Atom: Non-alcoholic steatohepatitis<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N203"}, {"label": "Liver enzyme analysis", "title": "Atom: Liver enzyme analysis<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N204"}, {"label": "Metabolic risk factors", "title": "Atom: Metabolic risk factors<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N205"}, {"label": "Hypertension", "title": "Atom: Hypertension<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N206"}, {"label": "Hyperlipidaemia", "title": "Atom: Hyperlipidaemia<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N207"}, {"label": "Glycaemic control", "title": "Atom: Glycaemic control<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N208"}, {"label": "Early identification", "title": "Atom: Early identification<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N209"}, {"label": "Modification", "title": "Atom: Modification<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N210"}, {"label": "Risk stratification", "title": "Atom: Risk stratification<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N211"}, {"label": "fails", "title": "Atom: fails<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N212"}, {"label": "confirms", "title": "Atom: confirms<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N213"}, {"label": "lies_in", "title": "Atom: lies_in<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N214"}, {"label": "modifies", "title": "Atom: modifies<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N215"}, {"label": "identifies", "title": "Atom: identifies<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N216"}, {"label": "stratifies", "title": "Atom: stratifies<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N217"}, {"label": "SENT_15", "title": "A scoring system developed at the Mayo clinic uses age, hyperglycemia, body mass\nindex, platelet count, albumin, and AST:ALT ratio to accurately differentiate\npatients with advanced fibrosis in NAFLD. the AST:ALT Ratio becomes considerably\nless specific in determining underlying disease with the development of\ncirrhosis, as the AST:ALT Ratio will increase across a broad range of diseases.\nthe AST:ALT Ratio is, however, useful in NAFLD patients known not to be abusing\nalcohol as a score of >1 should lead to the consideration that NAFLD patients\nknown not to be abusing alcohol may have developed cirrhosis.", "node_class": "SentenceNode", "color": "lightgreen", "shape": "box", "size": 20, "id": "N218"}, {"label": "Mayo Clinic", "title": "Atom: Mayo Clinic<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N219"}, {"label": "Mayo NAFLD Fibrosis Scoring System", "title": "Atom: Mayo NAFLD Fibrosis Scoring System<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N220"}, {"label": "Age", "title": "Atom: Age<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N221"}, {"label": "Hyperglycemia", "title": "Atom: Hyperglycemia<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N222"}, {"label": "Body Mass Index", "title": "Atom: Body Mass Index<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N223"}, {"label": "Platelet Count", "title": "Atom: Platelet Count<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N224"}, {"label": "AST:ALT Ratio", "title": "Atom: AST:ALT Ratio<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N225"}, {"label": "Advanced Fibrosis", "title": "Atom: Advanced Fibrosis<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N226"}, {"label": "Underlying Liver Disease", "title": "Atom: Underlying Liver Disease<br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N227"}, {"label": "Alcohol Abuse", "title": "Atom: Alcohol <PERSON><br>Type: ConceptNode", "node_class": "ConceptNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N228"}, {"label": "uses", "title": "Atom: uses<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N229"}, {"label": "leads_to", "title": "Atom: leads_to<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N230"}, {"label": "consideration", "title": "Atom: consideration<br>Type: PredicateNode", "node_class": "PredicateNode", "color": "skyblue", "shape": "ellipse", "size": 15, "id": "N231"}, {"label": "L1", "title": "Link: L1<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L1"}, {"label": "L2", "title": "Link: L2<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L2"}, {"label": "L3", "title": "Link: L3<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L3"}, {"label": "L4", "title": "Link: L4<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L4"}, {"label": "L5", "title": "Link: L5<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L5"}, {"label": "L6", "title": "Link: L6<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L6"}, {"label": "L7", "title": "Link: L7<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L7"}, {"label": "L8", "title": "Link: L8<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L8"}, {"label": "L9", "title": "Link: L9<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L9"}, {"label": "L10", "title": "Link: L10<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L10"}, {"label": "L11", "title": "Link: L11<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L11"}, {"label": "L12", "title": "Link: L12<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L12"}, {"label": "L13", "title": "Link: L13<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L13"}, {"label": "L14", "title": "Link: L14<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L14"}, {"label": "L15", "title": "Link: L15<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L15"}, {"label": "L16", "title": "Link: L16<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L16"}, {"label": "L17", "title": "Link: L17<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L17"}, {"label": "L18", "title": "Link: L18<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L18"}, {"label": "L19", "title": "Link: L19<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L19"}, {"label": "L20", "title": "Link: L20<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L20"}, {"label": "L21", "title": "Link: L21<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L21"}, {"label": "L22", "title": "Link: L22<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L22"}, {"label": "L23", "title": "Link: L23<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L23"}, {"label": "L24", "title": "Link: L24<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L24"}, {"label": "L25", "title": "Link: L25<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L25"}, {"label": "L26", "title": "Link: L26<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L26"}, {"label": "L27", "title": "Link: L27<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L27"}, {"label": "L28", "title": "Link: L28<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L28"}, {"label": "L29", "title": "Link: L29<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L29"}, {"label": "L30", "title": "Link: L30<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L30"}, {"label": "L31", "title": "Link: L31<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L31"}, {"label": "L32", "title": "Link: L32<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L32"}, {"label": "L33", "title": "Link: L33<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L33"}, {"label": "L34", "title": "Link: L34<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L34"}, {"label": "L35", "title": "Link: L35<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L35"}, {"label": "L36", "title": "Link: L36<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L36"}, {"label": "L37", "title": "Link: L37<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L37"}, {"label": "L38", "title": "Link: L38<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L38"}, {"label": "L39", "title": "Link: L39<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L39"}, {"label": "L40", "title": "Link: L40<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L40"}, {"label": "L41", "title": "Link: L41<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L41"}, {"label": "L42", "title": "Link: L42<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L42"}, {"label": "L43", "title": "Link: L43<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L43"}, {"label": "L44", "title": "Link: L44<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L44"}, {"label": "L45", "title": "Link: L45<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L45"}, {"label": "L46", "title": "Link: L46<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L46"}, {"label": "L47", "title": "Link: L47<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L47"}, {"label": "L48", "title": "Link: L48<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L48"}, {"label": "L49", "title": "Link: L49<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L49"}, {"label": "L50", "title": "Link: L50<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L50"}, {"label": "L51", "title": "Link: L51<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L51"}, {"label": "L52", "title": "Link: L52<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L52"}, {"label": "L53", "title": "Link: L53<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L53"}, {"label": "L54", "title": "Link: L54<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L54"}, {"label": "L55", "title": "Link: L55<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L55"}, {"label": "L56", "title": "Link: L56<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L56"}, {"label": "L57", "title": "Link: L57<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L57"}, {"label": "L58", "title": "Link: L58<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L58"}, {"label": "L59", "title": "Link: L59<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L59"}, {"label": "L60", "title": "Link: L60<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L60"}, {"label": "L61", "title": "Link: L61<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L61"}, {"label": "L62", "title": "Link: L62<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L62"}, {"label": "L63", "title": "Link: L63<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L63"}, {"label": "L64", "title": "Link: L64<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L64"}, {"label": "L65", "title": "Link: L65<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L65"}, {"label": "L66", "title": "Link: L66<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L66"}, {"label": "L67", "title": "Link: L67<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L67"}, {"label": "L68", "title": "Link: L68<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L68"}, {"label": "L69", "title": "Link: L69<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L69"}, {"label": "L70", "title": "Link: L70<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L70"}, {"label": "L71", "title": "Link: L71<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L71"}, {"label": "L72", "title": "Link: L72<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L72"}, {"label": "L73", "title": "Link: L73<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L73"}, {"label": "L74", "title": "Link: L74<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L74"}, {"label": "L75", "title": "Link: L75<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L75"}, {"label": "L76", "title": "Link: L76<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L76"}, {"label": "L77", "title": "Link: L77<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L77"}, {"label": "L78", "title": "Link: L78<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L78"}, {"label": "L79", "title": "Link: L79<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L79"}, {"label": "L80", "title": "Link: L80<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L80"}, {"label": "L81", "title": "Link: L81<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L81"}, {"label": "L82", "title": "Link: L82<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L82"}, {"label": "L83", "title": "Link: L83<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L83"}, {"label": "L84", "title": "Link: L84<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L84"}, {"label": "L85", "title": "Link: L85<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L85"}, {"label": "L86", "title": "Link: L86<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L86"}, {"label": "L87", "title": "Link: L87<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L87"}, {"label": "L88", "title": "Link: L88<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L88"}, {"label": "L89", "title": "Link: L89<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L89"}, {"label": "L90", "title": "Link: L90<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L90"}, {"label": "L91", "title": "Link: L91<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L91"}, {"label": "L92", "title": "Link: L92<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L92"}, {"label": "L93", "title": "Link: L93<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L93"}, {"label": "L94", "title": "Link: L94<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L94"}, {"label": "L95", "title": "Link: L95<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L95"}, {"label": "L96", "title": "Link: L96<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L96"}, {"label": "L97", "title": "Link: L97<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L97"}, {"label": "L98", "title": "Link: L98<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L98"}, {"label": "L99", "title": "Link: L99<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L99"}, {"label": "L100", "title": "Link: L100<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L100"}, {"label": "L101", "title": "Link: L101<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L101"}, {"label": "L102", "title": "Link: L102<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L102"}, {"label": "L103", "title": "Link: L103<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L103"}, {"label": "L104", "title": "Link: L104<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L104"}, {"label": "L105", "title": "Link: L105<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L105"}, {"label": "L106", "title": "Link: L106<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L106"}, {"label": "L107", "title": "Link: L107<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L107"}, {"label": "L108", "title": "Link: L108<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L108"}, {"label": "L109", "title": "Link: L109<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L109"}, {"label": "L110", "title": "Link: L110<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L110"}, {"label": "L111", "title": "Link: L111<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L111"}, {"label": "L112", "title": "Link: L112<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L112"}, {"label": "L113", "title": "Link: L113<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L113"}, {"label": "L114", "title": "Link: L114<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L114"}, {"label": "L115", "title": "Link: L115<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L115"}, {"label": "L116", "title": "Link: L116<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L116"}, {"label": "L117", "title": "Link: L117<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L117"}, {"label": "L118", "title": "Link: L118<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L118"}, {"label": "L119", "title": "Link: L119<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L119"}, {"label": "L120", "title": "Link: L120<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L120"}, {"label": "L121", "title": "Link: L121<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L121"}, {"label": "L122", "title": "Link: L122<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L122"}, {"label": "L123", "title": "Link: L123<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L123"}, {"label": "L124", "title": "Link: L124<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L124"}, {"label": "L125", "title": "Link: L125<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L125"}, {"label": "L126", "title": "Link: L126<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L126"}, {"label": "L127", "title": "Link: L127<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L127"}, {"label": "L128", "title": "Link: L128<br>Type: ImplicationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L128"}, {"label": "L129", "title": "Link: L129<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L129"}, {"label": "L130", "title": "Link: L130<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L130"}, {"label": "L131", "title": "Link: L131<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L131"}, {"label": "L132", "title": "Link: L132<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L132"}, {"label": "L133", "title": "Link: L133<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L133"}, {"label": "L134", "title": "Link: L134<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L134"}, {"label": "L135", "title": "Link: L135<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L135"}, {"label": "L136", "title": "Link: L136<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L136"}, {"label": "L137", "title": "Link: L137<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L137"}, {"label": "L138", "title": "Link: L138<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L138"}, {"label": "L139", "title": "Link: L139<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L139"}, {"label": "L140", "title": "Link: L140<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L140"}, {"label": "L141", "title": "Link: L141<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L141"}, {"label": "L142", "title": "Link: L142<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L142"}, {"label": "L143", "title": "Link: L143<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L143"}, {"label": "L144", "title": "Link: L144<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L144"}, {"label": "L145", "title": "Link: L145<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L145"}, {"label": "L146", "title": "Link: L146<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L146"}, {"label": "L147", "title": "Link: L147<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L147"}, {"label": "L148", "title": "Link: L148<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L148"}, {"label": "L149", "title": "Link: L149<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L149"}, {"label": "L150", "title": "Link: L150<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L150"}, {"label": "L151", "title": "Link: L151<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L151"}, {"label": "L152", "title": "Link: L152<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L152"}, {"label": "L153", "title": "Link: L153<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L153"}, {"label": "L154", "title": "Link: L154<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L154"}, {"label": "L155", "title": "Link: L155<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L155"}, {"label": "L156", "title": "Link: L156<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L156"}, {"label": "L157", "title": "Link: L157<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L157"}, {"label": "L158", "title": "Link: L158<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L158"}, {"label": "L159", "title": "Link: L159<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L159"}, {"label": "L160", "title": "Link: L160<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L160"}, {"label": "L161", "title": "Link: L161<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L161"}, {"label": "L162", "title": "Link: L162<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L162"}, {"label": "L163", "title": "Link: L163<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L163"}, {"label": "L164", "title": "Link: L164<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L164"}, {"label": "L165", "title": "Link: L165<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L165"}, {"label": "L166", "title": "Link: L166<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L166"}, {"label": "L167", "title": "Link: L167<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L167"}, {"label": "L168", "title": "Link: L168<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L168"}, {"label": "L169", "title": "Link: L169<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L169"}, {"label": "L170", "title": "Link: L170<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L170"}, {"label": "L171", "title": "Link: L171<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L171"}, {"label": "L172", "title": "Link: L172<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L172"}, {"label": "L173", "title": "Link: L173<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L173"}, {"label": "L174", "title": "Link: L174<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L174"}, {"label": "L175", "title": "Link: L175<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L175"}, {"label": "L176", "title": "Link: L176<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L176"}, {"label": "L177", "title": "Link: L177<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L177"}, {"label": "L178", "title": "Link: L178<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L178"}, {"label": "L179", "title": "Link: L179<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L179"}, {"label": "L180", "title": "Link: L180<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L180"}, {"label": "L181", "title": "Link: L181<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L181"}, {"label": "L182", "title": "Link: L182<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L182"}, {"label": "L183", "title": "Link: L183<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L183"}, {"label": "L184", "title": "Link: L184<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L184"}, {"label": "L185", "title": "Link: L185<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L185"}, {"label": "L186", "title": "Link: L186<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L186"}, {"label": "L187", "title": "Link: L187<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L187"}, {"label": "L188", "title": "Link: L188<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L188"}, {"label": "L189", "title": "Link: L189<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L189"}, {"label": "L190", "title": "Link: L190<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L190"}, {"label": "L191", "title": "Link: L191<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L191"}, {"label": "L192", "title": "Link: L192<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L192"}, {"label": "L193", "title": "Link: L193<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L193"}, {"label": "L194", "title": "Link: L194<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L194"}, {"label": "L195", "title": "Link: L195<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L195"}, {"label": "L196", "title": "Link: L196<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L196"}, {"label": "L197", "title": "Link: L197<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L197"}, {"label": "L198", "title": "Link: L198<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L198"}, {"label": "L199", "title": "Link: L199<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L199"}, {"label": "L200", "title": "Link: L200<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L200"}, {"label": "L201", "title": "Link: L201<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L201"}, {"label": "L202", "title": "Link: L202<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L202"}, {"label": "L203", "title": "Link: L203<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L203"}, {"label": "L204", "title": "Link: L204<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L204"}, {"label": "L205", "title": "Link: L205<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L205"}, {"label": "L206", "title": "Link: L206<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L206"}, {"label": "L207", "title": "Link: L207<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L207"}, {"label": "L208", "title": "Link: L208<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L208"}, {"label": "L209", "title": "Link: L209<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L209"}, {"label": "L210", "title": "Link: L210<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L210"}, {"label": "L211", "title": "Link: L211<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L211"}, {"label": "L212", "title": "Link: L212<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L212"}, {"label": "L213", "title": "Link: L213<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L213"}, {"label": "L214", "title": "Link: L214<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L214"}, {"label": "L215", "title": "Link: L215<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L215"}, {"label": "L216", "title": "Link: L216<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L216"}, {"label": "L217", "title": "Link: L217<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L217"}, {"label": "L218", "title": "Link: L218<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L218"}, {"label": "L219", "title": "Link: L219<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L219"}, {"label": "L220", "title": "Link: L220<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L220"}, {"label": "L221", "title": "Link: L221<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L221"}, {"label": "L222", "title": "Link: L222<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L222"}, {"label": "L223", "title": "Link: L223<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L223"}, {"label": "L224", "title": "Link: L224<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L224"}, {"label": "L225", "title": "Link: L225<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L225"}, {"label": "L226", "title": "Link: L226<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L226"}, {"label": "L227", "title": "Link: L227<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L227"}, {"label": "L228", "title": "Link: L228<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L228"}, {"label": "L229", "title": "Link: L229<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L229"}, {"label": "L230", "title": "Link: L230<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L230"}, {"label": "L231", "title": "Link: L231<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L231"}, {"label": "L232", "title": "Link: L232<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L232"}, {"label": "L233", "title": "Link: L233<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L233"}, {"label": "L234", "title": "Link: L234<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L234"}, {"label": "L235", "title": "Link: L235<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L235"}, {"label": "L236", "title": "Link: L236<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L236"}, {"label": "L237", "title": "Link: L237<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L237"}, {"label": "L238", "title": "Link: L238<br>Type: EvaluationLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L238"}, {"label": "L239", "title": "Link: L239<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L239"}, {"label": "L240", "title": "Link: L240<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L240"}, {"label": "L241", "title": "Link: L241<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L241"}, {"label": "L242", "title": "Link: L242<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L242"}, {"label": "L243", "title": "Link: L243<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L243"}, {"label": "L244", "title": "Link: L244<br>Type: ContextLink", "node_class": "Link", "color": "lightcoral", "shape": "square", "size": 10, "id": "L244"}], "links": [{"source": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "target": "N19", "key": 0}, {"source": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "target": "N34", "key": 0}, {"source": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "target": "N35", "key": 0}, {"source": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "target": "N49", "key": 0}, {"source": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "target": "N65", "key": 0}, {"source": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "target": "N109", "key": 0}, {"source": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "target": "N110", "key": 0}, {"source": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "target": "N129", "key": 0}, {"source": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "target": "N156", "key": 0}, {"source": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "target": "N157", "key": 0}, {"source": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "target": "N174", "key": 0}, {"source": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "target": "N182", "key": 0}, {"source": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "target": "N193", "key": 0}, {"source": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "target": "N202", "key": 0}, {"source": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "target": "N218", "key": 0}, {"title": "EvaluationLink", "source": "L1", "target": "N124", "key": 0}, {"title": "EvaluationLink", "source": "L1", "target": "N3", "key": 0}, {"title": "EvaluationLink", "source": "L1", "target": "N4", "key": 0}, {"title": "EvaluationLink", "source": "L1", "target": "N2", "key": 0}, {"title": "EvaluationLink", "source": "L2", "target": "N124", "key": 0}, {"title": "EvaluationLink", "source": "L2", "target": "N6", "key": 0}, {"title": "EvaluationLink", "source": "L2", "target": "N27", "key": 0}, {"title": "EvaluationLink", "source": "L2", "target": "N28", "key": 0}, {"title": "EvaluationLink", "source": "L2", "target": "N5", "key": 0}, {"title": "EvaluationLink", "source": "L3", "target": "N9", "key": 0}, {"title": "EvaluationLink", "source": "L3", "target": "N10", "key": 0}, {"title": "EvaluationLink", "source": "L3", "target": "N124", "key": 0}, {"title": "EvaluationLink", "source": "L3", "target": "N11", "key": 0}, {"title": "EvaluationLink", "source": "L4", "target": "N124", "key": 0}, {"title": "EvaluationLink", "source": "L4", "target": "N13", "key": 0}, {"title": "EvaluationLink", "source": "L4", "target": "N114", "key": 0}, {"title": "EvaluationLink", "source": "L4", "target": "N12", "key": 0}, {"title": "EvaluationLink", "source": "L4", "target": "N15", "key": 0}, {"title": "EvaluationLink", "source": "L5", "target": "N16", "key": 0}, {"title": "EvaluationLink", "source": "L5", "target": "N18", "key": 0}, {"title": "EvaluationLink", "source": "L5", "target": "N17", "key": 0}, {"title": "ContextLink", "source": "L6", "target": "N20", "key": 0}, {"title": "ContextLink", "source": "L6", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L7", "target": "N20", "key": 0}, {"title": "ContextLink", "source": "L7", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L8", "target": "N20", "key": 0}, {"title": "ContextLink", "source": "L8", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L9", "target": "N20", "key": 0}, {"title": "ContextLink", "source": "L9", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L10", "target": "N20", "key": 0}, {"title": "ContextLink", "source": "L10", "target": "N202", "key": 0}, {"title": "EvaluationLink", "source": "L11", "target": "N16", "key": 0}, {"title": "EvaluationLink", "source": "L11", "target": "N12", "key": 0}, {"title": "EvaluationLink", "source": "L11", "target": "N29", "key": 0}, {"title": "EvaluationLink", "source": "L12", "target": "N16", "key": 0}, {"title": "EvaluationLink", "source": "L12", "target": "N22", "key": 0}, {"title": "EvaluationLink", "source": "L12", "target": "N29", "key": 0}, {"title": "EvaluationLink", "source": "L13", "target": "N16", "key": 0}, {"title": "EvaluationLink", "source": "L13", "target": "N23", "key": 0}, {"title": "EvaluationLink", "source": "L13", "target": "N29", "key": 0}, {"title": "EvaluationLink", "source": "L14", "target": "N24", "key": 0}, {"title": "EvaluationLink", "source": "L14", "target": "N26", "key": 0}, {"title": "EvaluationLink", "source": "L14", "target": "N30", "key": 0}, {"title": "EvaluationLink", "source": "L15", "target": "N25", "key": 0}, {"title": "EvaluationLink", "source": "L15", "target": "N26", "key": 0}, {"title": "EvaluationLink", "source": "L15", "target": "N30", "key": 0}, {"title": "EvaluationLink", "source": "L16", "target": "N124", "key": 0}, {"title": "EvaluationLink", "source": "L16", "target": "N27", "key": 0}, {"title": "EvaluationLink", "source": "L16", "target": "N28", "key": 0}, {"title": "EvaluationLink", "source": "L16", "target": "N31", "key": 0}, {"title": "EvaluationLink", "source": "L17", "target": "N124", "key": 0}, {"title": "EvaluationLink", "source": "L17", "target": "N9", "key": 0}, {"title": "EvaluationLink", "source": "L17", "target": "N10", "key": 0}, {"title": "EvaluationLink", "source": "L17", "target": "N32", "key": 0}, {"title": "ContextLink", "source": "L18", "target": "N34", "key": 0}, {"title": "ContextLink", "source": "L19", "target": "N34", "key": 0}, {"title": "ContextLink", "source": "L20", "target": "N34", "key": 0}, {"title": "ContextLink", "source": "L21", "target": "N34", "key": 0}, {"title": "ContextLink", "source": "L22", "target": "N34", "key": 0}, {"title": "ContextLink", "source": "L23", "target": "N34", "key": 0}, {"title": "ContextLink", "source": "L24", "target": "N34", "key": 0}, {"title": "EvaluationLink", "source": "L25", "target": "N37", "key": 0}, {"title": "EvaluationLink", "source": "L25", "target": "N38", "key": 0}, {"title": "EvaluationLink", "source": "L25", "target": "N48", "key": 0}, {"title": "EvaluationLink", "source": "L26", "target": "N36", "key": 0}, {"title": "EvaluationLink", "source": "L26", "target": "N41", "key": 0}, {"title": "EvaluationLink", "source": "L26", "target": "N42", "key": 0}, {"title": "EvaluationLink", "source": "L26", "target": "N45", "key": 0}, {"title": "EvaluationLink", "source": "L27", "target": "N36", "key": 0}, {"title": "EvaluationLink", "source": "L27", "target": "N44", "key": 0}, {"title": "EvaluationLink", "source": "L27", "target": "N43", "key": 0}, {"title": "EvaluationLink", "source": "L27", "target": "N47", "key": 0}, {"title": "EvaluationLink", "source": "L28", "target": "N37", "key": 0}, {"title": "EvaluationLink", "source": "L28", "target": "N44", "key": 0}, {"title": "EvaluationLink", "source": "L28", "target": "N39", "key": 0}, {"title": "EvaluationLink", "source": "L28", "target": "N40", "key": 0}, {"title": "EvaluationLink", "source": "L28", "target": "N46", "key": 0}, {"title": "ContextLink", "source": "L29", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L30", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L31", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L32", "target": "N202", "key": 0}, {"title": "EvaluationLink", "source": "L33", "target": "N50", "key": 0}, {"title": "EvaluationLink", "source": "L33", "target": "N54", "key": 0}, {"title": "EvaluationLink", "source": "L33", "target": "N94", "key": 0}, {"title": "EvaluationLink", "source": "L33", "target": "N24", "key": 0}, {"title": "EvaluationLink", "source": "L33", "target": "N59", "key": 0}, {"title": "EvaluationLink", "source": "L34", "target": "N50", "key": 0}, {"title": "EvaluationLink", "source": "L34", "target": "N54", "key": 0}, {"title": "EvaluationLink", "source": "L34", "target": "N94", "key": 0}, {"title": "EvaluationLink", "source": "L34", "target": "N24", "key": 0}, {"title": "EvaluationLink", "source": "L34", "target": "N62", "key": 0}, {"title": "EvaluationLink", "source": "L35", "target": "N50", "key": 0}, {"title": "EvaluationLink", "source": "L35", "target": "N54", "key": 0}, {"title": "EvaluationLink", "source": "L35", "target": "N59", "key": 0}, {"title": "EvaluationLink", "source": "L36", "target": "N54", "key": 0}, {"title": "EvaluationLink", "source": "L36", "target": "N94", "key": 0}, {"title": "EvaluationLink", "source": "L36", "target": "N53", "key": 0}, {"title": "EvaluationLink", "source": "L36", "target": "N61", "key": 0}, {"title": "EvaluationLink", "source": "L37", "target": "N51", "key": 0}, {"title": "EvaluationLink", "source": "L37", "target": "N54", "key": 0}, {"title": "EvaluationLink", "source": "L37", "target": "N94", "key": 0}, {"title": "EvaluationLink", "source": "L37", "target": "N60", "key": 0}, {"title": "EvaluationLink", "source": "L38", "target": "N51", "key": 0}, {"title": "EvaluationLink", "source": "L38", "target": "N54", "key": 0}, {"title": "EvaluationLink", "source": "L38", "target": "N94", "key": 0}, {"title": "EvaluationLink", "source": "L38", "target": "N62", "key": 0}, {"title": "EvaluationLink", "source": "L39", "target": "N58", "key": 0}, {"title": "EvaluationLink", "source": "L39", "target": "N60", "key": 0}, {"title": "EvaluationLink", "source": "L40", "target": "N64", "key": 0}, {"title": "EvaluationLink", "source": "L40", "target": "N57", "key": 0}, {"title": "EvaluationLink", "source": "L40", "target": "N58", "key": 0}, {"title": "EvaluationLink", "source": "L40", "target": "N63", "key": 0}, {"title": "ContextLink", "source": "L41", "target": "N49", "key": 0}, {"title": "ContextLink", "source": "L42", "target": "N49", "key": 0}, {"title": "ContextLink", "source": "L43", "target": "N49", "key": 0}, {"title": "ContextLink", "source": "L44", "target": "N49", "key": 0}, {"title": "ContextLink", "source": "L45", "target": "N49", "key": 0}, {"title": "ContextLink", "source": "L46", "target": "N49", "key": 0}, {"title": "ContextLink", "source": "L47", "target": "N49", "key": 0}, {"title": "ContextLink", "source": "L48", "target": "N49", "key": 0}, {"title": "EvaluationLink", "source": "L49", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L49", "target": "N68", "key": 0}, {"title": "EvaluationLink", "source": "L49", "target": "N46", "key": 0}, {"title": "EvaluationLink", "source": "L50", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L50", "target": "N87", "key": 0}, {"title": "EvaluationLink", "source": "L50", "target": "N69", "key": 0}, {"title": "EvaluationLink", "source": "L50", "target": "N70", "key": 0}, {"title": "EvaluationLink", "source": "L50", "target": "N81", "key": 0}, {"title": "EvaluationLink", "source": "L51", "target": "N56", "key": 0}, {"title": "EvaluationLink", "source": "L51", "target": "N74", "key": 0}, {"title": "EvaluationLink", "source": "L51", "target": "N83", "key": 0}, {"title": "EvaluationLink", "source": "L52", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L52", "target": "N88", "key": 0}, {"title": "EvaluationLink", "source": "L52", "target": "N71", "key": 0}, {"title": "EvaluationLink", "source": "L52", "target": "N82", "key": 0}, {"title": "EvaluationLink", "source": "L53", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L53", "target": "N88", "key": 0}, {"title": "EvaluationLink", "source": "L53", "target": "N72", "key": 0}, {"title": "EvaluationLink", "source": "L53", "target": "N82", "key": 0}, {"title": "EvaluationLink", "source": "L54", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L54", "target": "N88", "key": 0}, {"title": "EvaluationLink", "source": "L54", "target": "N73", "key": 0}, {"title": "EvaluationLink", "source": "L54", "target": "N82", "key": 0}, {"title": "EvaluationLink", "source": "L55", "target": "N76", "key": 0}, {"title": "EvaluationLink", "source": "L55", "target": "N86", "key": 0}, {"title": "EvaluationLink", "source": "L55", "target": "N54", "key": 0}, {"title": "EvaluationLink", "source": "L55", "target": "N84", "key": 0}, {"title": "EvaluationLink", "source": "L56", "target": "N76", "key": 0}, {"title": "EvaluationLink", "source": "L56", "target": "N75", "key": 0}, {"title": "EvaluationLink", "source": "L56", "target": "N89", "key": 0}, {"title": "EvaluationLink", "source": "L56", "target": "N84", "key": 0}, {"title": "EvaluationLink", "source": "L57", "target": "N76", "key": 0}, {"title": "EvaluationLink", "source": "L57", "target": "N94", "key": 0}, {"title": "EvaluationLink", "source": "L57", "target": "N84", "key": 0}, {"title": "EvaluationLink", "source": "L58", "target": "N77", "key": 0}, {"title": "EvaluationLink", "source": "L58", "target": "N76", "key": 0}, {"title": "EvaluationLink", "source": "L58", "target": "N85", "key": 0}, {"title": "EvaluationLink", "source": "L59", "target": "N78", "key": 0}, {"title": "EvaluationLink", "source": "L59", "target": "N76", "key": 0}, {"title": "EvaluationLink", "source": "L59", "target": "N85", "key": 0}, {"title": "EvaluationLink", "source": "L60", "target": "N79", "key": 0}, {"title": "EvaluationLink", "source": "L60", "target": "N76", "key": 0}, {"title": "EvaluationLink", "source": "L60", "target": "N85", "key": 0}, {"title": "EvaluationLink", "source": "L61", "target": "N80", "key": 0}, {"title": "EvaluationLink", "source": "L61", "target": "N76", "key": 0}, {"title": "EvaluationLink", "source": "L61", "target": "N85", "key": 0}, {"title": "ContextLink", "source": "L62", "target": "N65", "key": 0}, {"title": "ContextLink", "source": "L63", "target": "N65", "key": 0}, {"title": "ContextLink", "source": "L64", "target": "N65", "key": 0}, {"title": "ContextLink", "source": "L65", "target": "N65", "key": 0}, {"title": "ContextLink", "source": "L66", "target": "N65", "key": 0}, {"title": "ContextLink", "source": "L67", "target": "N65", "key": 0}, {"title": "ContextLink", "source": "L68", "target": "N65", "key": 0}, {"title": "ContextLink", "source": "L69", "target": "N65", "key": 0}, {"title": "ContextLink", "source": "L70", "target": "N65", "key": 0}, {"title": "ContextLink", "source": "L71", "target": "N65", "key": 0}, {"title": "ContextLink", "source": "L72", "target": "N65", "key": 0}, {"title": "ContextLink", "source": "L73", "target": "N65", "key": 0}, {"title": "ContextLink", "source": "L74", "target": "N65", "key": 0}, {"title": "EvaluationLink", "source": "L75", "target": "N54", "key": 0}, {"title": "EvaluationLink", "source": "L75", "target": "N94", "key": 0}, {"title": "EvaluationLink", "source": "L75", "target": "N112", "key": 0}, {"title": "EvaluationLink", "source": "L75", "target": "N103", "key": 0}, {"title": "EvaluationLink", "source": "L76", "target": "N95", "key": 0}, {"title": "EvaluationLink", "source": "L76", "target": "N112", "key": 0}, {"title": "EvaluationLink", "source": "L76", "target": "N104", "key": 0}, {"title": "EvaluationLink", "source": "L77", "target": "N95", "key": 0}, {"title": "EvaluationLink", "source": "L77", "target": "N112", "key": 0}, {"title": "EvaluationLink", "source": "L77", "target": "N105", "key": 0}, {"title": "EvaluationLink", "source": "L78", "target": "N101", "key": 0}, {"title": "EvaluationLink", "source": "L78", "target": "N44", "key": 0}, {"title": "EvaluationLink", "source": "L78", "target": "N106", "key": 0}, {"title": "EvaluationLink", "source": "L79", "target": "N102", "key": 0}, {"title": "EvaluationLink", "source": "L79", "target": "N44", "key": 0}, {"title": "EvaluationLink", "source": "L79", "target": "N106", "key": 0}, {"title": "EvaluationLink", "source": "L80", "target": "N44", "key": 0}, {"title": "EvaluationLink", "source": "L80", "target": "N96", "key": 0}, {"title": "EvaluationLink", "source": "L80", "target": "N107", "key": 0}, {"title": "EvaluationLink", "source": "L81", "target": "N44", "key": 0}, {"title": "EvaluationLink", "source": "L81", "target": "N97", "key": 0}, {"title": "EvaluationLink", "source": "L81", "target": "N108", "key": 0}, {"title": "EvaluationLink", "source": "L82", "target": "N44", "key": 0}, {"title": "EvaluationLink", "source": "L82", "target": "N99", "key": 0}, {"title": "EvaluationLink", "source": "L82", "target": "N62", "key": 0}, {"title": "EvaluationLink", "source": "L83", "target": "N44", "key": 0}, {"title": "EvaluationLink", "source": "L83", "target": "N100", "key": 0}, {"title": "EvaluationLink", "source": "L83", "target": "N62", "key": 0}, {"title": "ContextLink", "source": "L84", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L85", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L86", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L87", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L88", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L89", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L90", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L91", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L92", "target": "N202", "key": 0}, {"title": "EvaluationLink", "source": "L93", "target": "N74", "key": 0}, {"title": "EvaluationLink", "source": "L93", "target": "N54", "key": 0}, {"title": "EvaluationLink", "source": "L93", "target": "N115", "key": 0}, {"title": "EvaluationLink", "source": "L93", "target": "N111", "key": 0}, {"title": "EvaluationLink", "source": "L93", "target": "N112", "key": 0}, {"title": "EvaluationLink", "source": "L93", "target": "N116", "key": 0}, {"title": "EvaluationLink", "source": "L93", "target": "N113", "key": 0}, {"title": "EvaluationLink", "source": "L94", "target": "N111", "key": 0}, {"title": "EvaluationLink", "source": "L94", "target": "N112", "key": 0}, {"title": "EvaluationLink", "source": "L94", "target": "N114", "key": 0}, {"title": "EvaluationLink", "source": "L95", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L95", "target": "N117", "key": 0}, {"title": "EvaluationLink", "source": "L95", "target": "N118", "key": 0}, {"title": "EvaluationLink", "source": "L95", "target": "N112", "key": 0}, {"title": "EvaluationLink", "source": "L95", "target": "N119", "key": 0}, {"title": "EvaluationLink", "source": "L96", "target": "N194", "key": 0}, {"title": "EvaluationLink", "source": "L96", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L96", "target": "N118", "key": 0}, {"title": "EvaluationLink", "source": "L96", "target": "N112", "key": 0}, {"title": "EvaluationLink", "source": "L96", "target": "N122", "key": 0}, {"title": "EvaluationLink", "source": "L97", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L97", "target": "N118", "key": 0}, {"title": "EvaluationLink", "source": "L97", "target": "N112", "key": 0}, {"title": "EvaluationLink", "source": "L97", "target": "N123", "key": 0}, {"title": "EvaluationLink", "source": "L97", "target": "N121", "key": 0}, {"title": "EvaluationLink", "source": "L98", "target": "N124", "key": 0}, {"title": "EvaluationLink", "source": "L98", "target": "N44", "key": 0}, {"title": "EvaluationLink", "source": "L98", "target": "N125", "key": 0}, {"title": "EvaluationLink", "source": "L98", "target": "N127", "key": 0}, {"title": "EvaluationLink", "source": "L99", "target": "N124", "key": 0}, {"title": "EvaluationLink", "source": "L99", "target": "N44", "key": 0}, {"title": "EvaluationLink", "source": "L99", "target": "N112", "key": 0}, {"title": "EvaluationLink", "source": "L99", "target": "N127", "key": 0}, {"title": "ContextLink", "source": "L100", "target": "N128", "key": 0}, {"title": "ContextLink", "source": "L100", "target": "N110", "key": 0}, {"title": "ContextLink", "source": "L101", "target": "N128", "key": 0}, {"title": "ContextLink", "source": "L101", "target": "N110", "key": 0}, {"title": "ContextLink", "source": "L102", "target": "N128", "key": 0}, {"title": "ContextLink", "source": "L102", "target": "N110", "key": 0}, {"title": "ContextLink", "source": "L103", "target": "N128", "key": 0}, {"title": "ContextLink", "source": "L103", "target": "N110", "key": 0}, {"title": "ContextLink", "source": "L104", "target": "N128", "key": 0}, {"title": "ContextLink", "source": "L104", "target": "N110", "key": 0}, {"title": "ContextLink", "source": "L105", "target": "N128", "key": 0}, {"title": "ContextLink", "source": "L105", "target": "N110", "key": 0}, {"title": "ContextLink", "source": "L106", "target": "N128", "key": 0}, {"title": "ContextLink", "source": "L106", "target": "N110", "key": 0}, {"title": "EvaluationLink", "source": "L107", "target": "N55", "key": 0}, {"title": "EvaluationLink", "source": "L107", "target": "N54", "key": 0}, {"title": "EvaluationLink", "source": "L107", "target": "N133", "key": 0}, {"title": "EvaluationLink", "source": "L107", "target": "N134", "key": 0}, {"title": "EvaluationLink", "source": "L107", "target": "N135", "key": 0}, {"title": "EvaluationLink", "source": "L108", "target": "N100", "key": 0}, {"title": "EvaluationLink", "source": "L108", "target": "N136", "key": 0}, {"title": "EvaluationLink", "source": "L109", "target": "N130", "key": 0}, {"title": "EvaluationLink", "source": "L109", "target": "N125", "key": 0}, {"title": "EvaluationLink", "source": "L109", "target": "N143", "key": 0}, {"title": "EvaluationLink", "source": "L109", "target": "N137", "key": 0}, {"title": "EvaluationLink", "source": "L110", "target": "N125", "key": 0}, {"title": "EvaluationLink", "source": "L110", "target": "N130", "key": 0}, {"title": "EvaluationLink", "source": "L110", "target": "N142", "key": 0}, {"title": "EvaluationLink", "source": "L110", "target": "N62", "key": 0}, {"title": "EvaluationLink", "source": "L111", "target": "N74", "key": 0}, {"title": "EvaluationLink", "source": "L111", "target": "N131", "key": 0}, {"title": "EvaluationLink", "source": "L111", "target": "N138", "key": 0}, {"title": "EvaluationLink", "source": "L112", "target": "N131", "key": 0}, {"title": "EvaluationLink", "source": "L112", "target": "N125", "key": 0}, {"title": "EvaluationLink", "source": "L112", "target": "N139", "key": 0}, {"title": "EvaluationLink", "source": "L113", "target": "N131", "key": 0}, {"title": "EvaluationLink", "source": "L113", "target": "N132", "key": 0}, {"title": "EvaluationLink", "source": "L113", "target": "N140", "key": 0}, {"title": "EvaluationLink", "source": "L114", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L114", "target": "N132", "key": 0}, {"title": "EvaluationLink", "source": "L114", "target": "N144", "key": 0}, {"title": "EvaluationLink", "source": "L114", "target": "N140", "key": 0}, {"title": "EvaluationLink", "source": "L115", "target": "N125", "key": 0}, {"title": "EvaluationLink", "source": "L115", "target": "N132", "key": 0}, {"title": "EvaluationLink", "source": "L115", "target": "N142", "key": 0}, {"title": "EvaluationLink", "source": "L115", "target": "N62", "key": 0}, {"title": "ContextLink", "source": "L116", "target": "N129", "key": 0}, {"title": "ContextLink", "source": "L117", "target": "N129", "key": 0}, {"title": "ContextLink", "source": "L118", "target": "N129", "key": 0}, {"title": "ContextLink", "source": "L119", "target": "N129", "key": 0}, {"title": "ContextLink", "source": "L120", "target": "N129", "key": 0}, {"title": "ContextLink", "source": "L121", "target": "N129", "key": 0}, {"title": "ContextLink", "source": "L122", "target": "N129", "key": 0}, {"title": "ContextLink", "source": "L123", "target": "N129", "key": 0}, {"title": "ContextLink", "source": "L124", "target": "N129", "key": 0}, {"title": "EvaluationLink", "source": "L125", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L125", "target": "N145", "key": 0}, {"title": "EvaluationLink", "source": "L125", "target": "N118", "key": 0}, {"title": "EvaluationLink", "source": "L125", "target": "N82", "key": 0}, {"title": "EvaluationLink", "source": "L126", "target": "N146", "key": 0}, {"title": "EvaluationLink", "source": "L126", "target": "N36", "key": 0}, {"title": "EvaluationLink", "source": "L126", "target": "N59", "key": 0}, {"title": "EvaluationLink", "source": "L127", "target": "N36", "key": 0}, {"title": "EvaluationLink", "source": "L127", "target": "N94", "key": 0}, {"title": "EvaluationLink", "source": "L127", "target": "N59", "key": 0}, {"title": "EvaluationLink", "source": "L129", "target": "N147", "key": 0}, {"title": "EvaluationLink", "source": "L129", "target": "N148", "key": 0}, {"title": "EvaluationLink", "source": "L129", "target": "N149", "key": 0}, {"title": "EvaluationLink", "source": "L129", "target": "N146", "key": 0}, {"title": "EvaluationLink", "source": "L129", "target": "N12", "key": 0}, {"title": "EvaluationLink", "source": "L129", "target": "N82", "key": 0}, {"title": "EvaluationLink", "source": "L130", "target": "N146", "key": 0}, {"title": "EvaluationLink", "source": "L130", "target": "N12", "key": 0}, {"title": "EvaluationLink", "source": "L130", "target": "N85", "key": 0}, {"title": "EvaluationLink", "source": "L131", "target": "N151", "key": 0}, {"title": "EvaluationLink", "source": "L131", "target": "N150", "key": 0}, {"title": "EvaluationLink", "source": "L131", "target": "N85", "key": 0}, {"title": "EvaluationLink", "source": "L132", "target": "N152", "key": 0}, {"title": "EvaluationLink", "source": "L132", "target": "N150", "key": 0}, {"title": "EvaluationLink", "source": "L132", "target": "N85", "key": 0}, {"title": "EvaluationLink", "source": "L133", "target": "N153", "key": 0}, {"title": "EvaluationLink", "source": "L133", "target": "N150", "key": 0}, {"title": "EvaluationLink", "source": "L133", "target": "N85", "key": 0}, {"title": "EvaluationLink", "source": "L134", "target": "N146", "key": 0}, {"title": "EvaluationLink", "source": "L134", "target": "N153", "key": 0}, {"title": "EvaluationLink", "source": "L134", "target": "N85", "key": 0}, {"title": "EvaluationLink", "source": "L135", "target": "N150", "key": 0}, {"title": "EvaluationLink", "source": "L135", "target": "N154", "key": 0}, {"title": "EvaluationLink", "source": "L135", "target": "N155", "key": 0}, {"title": "EvaluationLink", "source": "L136", "target": "N146", "key": 0}, {"title": "EvaluationLink", "source": "L136", "target": "N94", "key": 0}, {"title": "EvaluationLink", "source": "L136", "target": "N62", "key": 0}, {"title": "EvaluationLink", "source": "L137", "target": "N146", "key": 0}, {"title": "EvaluationLink", "source": "L137", "target": "N150", "key": 0}, {"title": "EvaluationLink", "source": "L137", "target": "N62", "key": 0}, {"title": "ContextLink", "source": "L138", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L139", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L140", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L141", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L142", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L143", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L144", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L145", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L146", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L147", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L148", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L149", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L150", "target": "N202", "key": 0}, {"title": "EvaluationLink", "source": "L151", "target": "N166", "key": 0}, {"title": "EvaluationLink", "source": "L151", "target": "N158", "key": 0}, {"title": "EvaluationLink", "source": "L151", "target": "N170", "key": 0}, {"title": "EvaluationLink", "source": "L152", "target": "N166", "key": 0}, {"title": "EvaluationLink", "source": "L152", "target": "N159", "key": 0}, {"title": "EvaluationLink", "source": "L152", "target": "N171", "key": 0}, {"title": "EvaluationLink", "source": "L153", "target": "N166", "key": 0}, {"title": "EvaluationLink", "source": "L153", "target": "N160", "key": 0}, {"title": "EvaluationLink", "source": "L153", "target": "N171", "key": 0}, {"title": "EvaluationLink", "source": "L154", "target": "N169", "key": 0}, {"title": "EvaluationLink", "source": "L154", "target": "N161", "key": 0}, {"title": "EvaluationLink", "source": "L154", "target": "N162", "key": 0}, {"title": "EvaluationLink", "source": "L154", "target": "N172", "key": 0}, {"title": "EvaluationLink", "source": "L155", "target": "N166", "key": 0}, {"title": "EvaluationLink", "source": "L155", "target": "N163", "key": 0}, {"title": "EvaluationLink", "source": "L155", "target": "N164", "key": 0}, {"title": "EvaluationLink", "source": "L155", "target": "N172", "key": 0}, {"title": "EvaluationLink", "source": "L156", "target": "N166", "key": 0}, {"title": "EvaluationLink", "source": "L156", "target": "N163", "key": 0}, {"title": "EvaluationLink", "source": "L156", "target": "N165", "key": 0}, {"title": "EvaluationLink", "source": "L156", "target": "N172", "key": 0}, {"title": "EvaluationLink", "source": "L157", "target": "N167", "key": 0}, {"title": "EvaluationLink", "source": "L157", "target": "N94", "key": 0}, {"title": "EvaluationLink", "source": "L157", "target": "N59", "key": 0}, {"title": "EvaluationLink", "source": "L158", "target": "N94", "key": 0}, {"title": "EvaluationLink", "source": "L158", "target": "N167", "key": 0}, {"title": "EvaluationLink", "source": "L158", "target": "N173", "key": 0}, {"title": "EvaluationLink", "source": "L159", "target": "N169", "key": 0}, {"title": "EvaluationLink", "source": "L159", "target": "N168", "key": 0}, {"title": "EvaluationLink", "source": "L159", "target": "N172", "key": 0}, {"title": "EvaluationLink", "source": "L160", "target": "N167", "key": 0}, {"title": "EvaluationLink", "source": "L160", "target": "N94", "key": 0}, {"title": "EvaluationLink", "source": "L160", "target": "N62", "key": 0}, {"title": "ContextLink", "source": "L161", "target": "N157", "key": 0}, {"title": "ContextLink", "source": "L162", "target": "N157", "key": 0}, {"title": "ContextLink", "source": "L163", "target": "N157", "key": 0}, {"title": "ContextLink", "source": "L164", "target": "N157", "key": 0}, {"title": "ContextLink", "source": "L165", "target": "N157", "key": 0}, {"title": "ContextLink", "source": "L166", "target": "N157", "key": 0}, {"title": "ContextLink", "source": "L167", "target": "N157", "key": 0}, {"title": "ContextLink", "source": "L168", "target": "N157", "key": 0}, {"title": "ContextLink", "source": "L169", "target": "N157", "key": 0}, {"title": "ContextLink", "source": "L170", "target": "N157", "key": 0}, {"title": "EvaluationLink", "source": "L171", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L171", "target": "N154", "key": 0}, {"title": "EvaluationLink", "source": "L171", "target": "N12", "key": 0}, {"title": "EvaluationLink", "source": "L171", "target": "N196", "key": 0}, {"title": "EvaluationLink", "source": "L171", "target": "N45", "key": 0}, {"title": "EvaluationLink", "source": "L172", "target": "N74", "key": 0}, {"title": "EvaluationLink", "source": "L172", "target": "N178", "key": 0}, {"title": "EvaluationLink", "source": "L172", "target": "N62", "key": 0}, {"title": "EvaluationLink", "source": "L173", "target": "N56", "key": 0}, {"title": "EvaluationLink", "source": "L173", "target": "N178", "key": 0}, {"title": "EvaluationLink", "source": "L173", "target": "N62", "key": 0}, {"title": "EvaluationLink", "source": "L174", "target": "N178", "key": 0}, {"title": "EvaluationLink", "source": "L174", "target": "N183", "key": 0}, {"title": "EvaluationLink", "source": "L174", "target": "N180", "key": 0}, {"title": "EvaluationLink", "source": "L174", "target": "N85", "key": 0}, {"title": "EvaluationLink", "source": "L175", "target": "N178", "key": 0}, {"title": "EvaluationLink", "source": "L175", "target": "N183", "key": 0}, {"title": "EvaluationLink", "source": "L175", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L175", "target": "N59", "key": 0}, {"title": "EvaluationLink", "source": "L176", "target": "N178", "key": 0}, {"title": "EvaluationLink", "source": "L176", "target": "N183", "key": 0}, {"title": "EvaluationLink", "source": "L176", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L176", "target": "N85", "key": 0}, {"title": "EvaluationLink", "source": "L177", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L177", "target": "N181", "key": 0}, {"title": "EvaluationLink", "source": "L177", "target": "N104", "key": 0}, {"title": "ContextLink", "source": "L178", "target": "N174", "key": 0}, {"title": "ContextLink", "source": "L179", "target": "N174", "key": 0}, {"title": "ContextLink", "source": "L180", "target": "N174", "key": 0}, {"title": "ContextLink", "source": "L181", "target": "N174", "key": 0}, {"title": "ContextLink", "source": "L182", "target": "N174", "key": 0}, {"title": "ContextLink", "source": "L183", "target": "N174", "key": 0}, {"title": "ContextLink", "source": "L184", "target": "N174", "key": 0}, {"title": "EvaluationLink", "source": "L185", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L185", "target": "N145", "key": 0}, {"title": "EvaluationLink", "source": "L185", "target": "N154", "key": 0}, {"title": "EvaluationLink", "source": "L185", "target": "N103", "key": 0}, {"title": "EvaluationLink", "source": "L186", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L186", "target": "N184", "key": 0}, {"title": "EvaluationLink", "source": "L186", "target": "N154", "key": 0}, {"title": "EvaluationLink", "source": "L186", "target": "N103", "key": 0}, {"title": "EvaluationLink", "source": "L187", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L187", "target": "N184", "key": 0}, {"title": "EvaluationLink", "source": "L187", "target": "N154", "key": 0}, {"title": "EvaluationLink", "source": "L187", "target": "N189", "key": 0}, {"title": "EvaluationLink", "source": "L188", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L188", "target": "N181", "key": 0}, {"title": "EvaluationLink", "source": "L188", "target": "N196", "key": 0}, {"title": "EvaluationLink", "source": "L188", "target": "N189", "key": 0}, {"title": "EvaluationLink", "source": "L189", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L189", "target": "N184", "key": 0}, {"title": "EvaluationLink", "source": "L189", "target": "N187", "key": 0}, {"title": "EvaluationLink", "source": "L189", "target": "N42", "key": 0}, {"title": "EvaluationLink", "source": "L189", "target": "N12", "key": 0}, {"title": "EvaluationLink", "source": "L189", "target": "N190", "key": 0}, {"title": "EvaluationLink", "source": "L190", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L190", "target": "N184", "key": 0}, {"title": "EvaluationLink", "source": "L190", "target": "N188", "key": 0}, {"title": "EvaluationLink", "source": "L190", "target": "N190", "key": 0}, {"title": "EvaluationLink", "source": "L191", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L191", "target": "N145", "key": 0}, {"title": "EvaluationLink", "source": "L191", "target": "N183", "key": 0}, {"title": "EvaluationLink", "source": "L191", "target": "N103", "key": 0}, {"title": "EvaluationLink", "source": "L192", "target": "N183", "key": 0}, {"title": "EvaluationLink", "source": "L192", "target": "N154", "key": 0}, {"title": "EvaluationLink", "source": "L192", "target": "N191", "key": 0}, {"title": "EvaluationLink", "source": "L193", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L193", "target": "N184", "key": 0}, {"title": "EvaluationLink", "source": "L193", "target": "N142", "key": 0}, {"title": "EvaluationLink", "source": "L193", "target": "N62", "key": 0}, {"title": "ContextLink", "source": "L194", "target": "N182", "key": 0}, {"title": "ContextLink", "source": "L195", "target": "N182", "key": 0}, {"title": "ContextLink", "source": "L196", "target": "N182", "key": 0}, {"title": "ContextLink", "source": "L197", "target": "N182", "key": 0}, {"title": "ContextLink", "source": "L198", "target": "N182", "key": 0}, {"title": "ContextLink", "source": "L199", "target": "N182", "key": 0}, {"title": "ContextLink", "source": "L200", "target": "N182", "key": 0}, {"title": "ContextLink", "source": "L201", "target": "N182", "key": 0}, {"title": "ContextLink", "source": "L202", "target": "N182", "key": 0}, {"title": "EvaluationLink", "source": "L203", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L203", "target": "N100", "key": 0}, {"title": "EvaluationLink", "source": "L203", "target": "N62", "key": 0}, {"title": "EvaluationLink", "source": "L204", "target": "N66", "key": 0}, {"title": "EvaluationLink", "source": "L204", "target": "N197", "key": 0}, {"title": "EvaluationLink", "source": "L204", "target": "N62", "key": 0}, {"title": "EvaluationLink", "source": "L205", "target": "N57", "key": 0}, {"title": "EvaluationLink", "source": "L205", "target": "N199", "key": 0}, {"title": "EvaluationLink", "source": "L205", "target": "N198", "key": 0}, {"title": "EvaluationLink", "source": "L206", "target": "N57", "key": 0}, {"title": "EvaluationLink", "source": "L206", "target": "N200", "key": 0}, {"title": "EvaluationLink", "source": "L206", "target": "N198", "key": 0}, {"title": "EvaluationLink", "source": "L207", "target": "N56", "key": 0}, {"title": "EvaluationLink", "source": "L207", "target": "N200", "key": 0}, {"title": "EvaluationLink", "source": "L207", "target": "N201", "key": 0}, {"title": "ContextLink", "source": "L208", "target": "N193", "key": 0}, {"title": "ContextLink", "source": "L209", "target": "N193", "key": 0}, {"title": "ContextLink", "source": "L210", "target": "N193", "key": 0}, {"title": "ContextLink", "source": "L211", "target": "N193", "key": 0}, {"title": "ContextLink", "source": "L212", "target": "N193", "key": 0}, {"title": "EvaluationLink", "source": "L213", "target": "N204", "key": 0}, {"title": "EvaluationLink", "source": "L213", "target": "N194", "key": 0}, {"title": "EvaluationLink", "source": "L213", "target": "N121", "key": 0}, {"title": "EvaluationLink", "source": "L213", "target": "N44", "key": 0}, {"title": "EvaluationLink", "source": "L213", "target": "N212", "key": 0}, {"title": "EvaluationLink", "source": "L214", "target": "N44", "key": 0}, {"title": "EvaluationLink", "source": "L214", "target": "N203", "key": 0}, {"title": "EvaluationLink", "source": "L214", "target": "N100", "key": 0}, {"title": "EvaluationLink", "source": "L214", "target": "N62", "key": 0}, {"title": "EvaluationLink", "source": "L215", "target": "N44", "key": 0}, {"title": "EvaluationLink", "source": "L215", "target": "N118", "key": 0}, {"title": "EvaluationLink", "source": "L215", "target": "N100", "key": 0}, {"title": "EvaluationLink", "source": "L215", "target": "N62", "key": 0}, {"title": "EvaluationLink", "source": "L216", "target": "N204", "key": 0}, {"title": "EvaluationLink", "source": "L216", "target": "N196", "key": 0}, {"title": "EvaluationLink", "source": "L216", "target": "N209", "key": 0}, {"title": "EvaluationLink", "source": "L216", "target": "N210", "key": 0}, {"title": "EvaluationLink", "source": "L216", "target": "N211", "key": 0}, {"title": "EvaluationLink", "source": "L216", "target": "N214", "key": 0}, {"title": "EvaluationLink", "source": "L217", "target": "N204", "key": 0}, {"title": "EvaluationLink", "source": "L217", "target": "N205", "key": 0}, {"title": "EvaluationLink", "source": "L217", "target": "N216", "key": 0}, {"title": "EvaluationLink", "source": "L218", "target": "N204", "key": 0}, {"title": "EvaluationLink", "source": "L218", "target": "N205", "key": 0}, {"title": "EvaluationLink", "source": "L218", "target": "N215", "key": 0}, {"title": "EvaluationLink", "source": "L219", "target": "N204", "key": 0}, {"title": "EvaluationLink", "source": "L219", "target": "N211", "key": 0}, {"title": "EvaluationLink", "source": "L219", "target": "N217", "key": 0}, {"title": "EvaluationLink", "source": "L220", "target": "N205", "key": 0}, {"title": "EvaluationLink", "source": "L220", "target": "N206", "key": 0}, {"title": "EvaluationLink", "source": "L220", "target": "N215", "key": 0}, {"title": "EvaluationLink", "source": "L221", "target": "N205", "key": 0}, {"title": "EvaluationLink", "source": "L221", "target": "N207", "key": 0}, {"title": "EvaluationLink", "source": "L221", "target": "N215", "key": 0}, {"title": "EvaluationLink", "source": "L222", "target": "N205", "key": 0}, {"title": "EvaluationLink", "source": "L222", "target": "N208", "key": 0}, {"title": "EvaluationLink", "source": "L222", "target": "N215", "key": 0}, {"title": "ContextLink", "source": "L223", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L224", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L225", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L226", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L227", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L228", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L229", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L230", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L231", "target": "N202", "key": 0}, {"title": "ContextLink", "source": "L232", "target": "N202", "key": 0}, {"title": "EvaluationLink", "source": "L233", "target": "N220", "key": 0}, {"title": "EvaluationLink", "source": "L233", "target": "N221", "key": 0}, {"title": "EvaluationLink", "source": "L233", "target": "N222", "key": 0}, {"title": "EvaluationLink", "source": "L233", "target": "N223", "key": 0}, {"title": "EvaluationLink", "source": "L233", "target": "N224", "key": 0}, {"title": "EvaluationLink", "source": "L233", "target": "N25", "key": 0}, {"title": "EvaluationLink", "source": "L233", "target": "N225", "key": 0}, {"title": "EvaluationLink", "source": "L233", "target": "N229", "key": 0}, {"title": "EvaluationLink", "source": "L234", "target": "N220", "key": 0}, {"title": "EvaluationLink", "source": "L234", "target": "N226", "key": 0}, {"title": "EvaluationLink", "source": "L234", "target": "N196", "key": 0}, {"title": "EvaluationLink", "source": "L234", "target": "N45", "key": 0}, {"title": "EvaluationLink", "source": "L235", "target": "N225", "key": 0}, {"title": "EvaluationLink", "source": "L235", "target": "N227", "key": 0}, {"title": "EvaluationLink", "source": "L235", "target": "N118", "key": 0}, {"title": "EvaluationLink", "source": "L235", "target": "N121", "key": 0}, {"title": "EvaluationLink", "source": "L236", "target": "N225", "key": 0}, {"title": "EvaluationLink", "source": "L236", "target": "N227", "key": 0}, {"title": "EvaluationLink", "source": "L236", "target": "N59", "key": 0}, {"title": "EvaluationLink", "source": "L237", "target": "N225", "key": 0}, {"title": "EvaluationLink", "source": "L237", "target": "N3", "key": 0}, {"title": "EvaluationLink", "source": "L237", "target": "N62", "key": 0}, {"title": "EvaluationLink", "source": "L238", "target": "N225", "key": 0}, {"title": "EvaluationLink", "source": "L238", "target": "N145", "key": 0}, {"title": "EvaluationLink", "source": "L238", "target": "N196", "key": 0}, {"title": "EvaluationLink", "source": "L238", "target": "N228", "key": 0}, {"title": "EvaluationLink", "source": "L238", "target": "N230", "key": 0}, {"title": "ContextLink", "source": "L239", "target": "N218", "key": 0}, {"title": "ContextLink", "source": "L240", "target": "N218", "key": 0}, {"title": "ContextLink", "source": "L241", "target": "N218", "key": 0}, {"title": "ContextLink", "source": "L242", "target": "N218", "key": 0}, {"title": "ContextLink", "source": "L243", "target": "N218", "key": 0}, {"title": "ContextLink", "source": "L244", "target": "N218", "key": 0}]}