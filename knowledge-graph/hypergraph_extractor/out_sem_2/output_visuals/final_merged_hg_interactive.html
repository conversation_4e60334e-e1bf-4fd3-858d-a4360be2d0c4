<html>
    <head>
        <meta charset="utf-8">
        
            <script>function neighbourhoodHighlight(params) {
  // console.log("in nieghbourhoodhighlight");
  allNodes = nodes.get({ returnType: "Object" });
  // originalNodes = JSON.parse(JSON.stringify(allNodes));
  // if something is selected:
  if (params.nodes.length > 0) {
    highlightActive = true;
    var i, j;
    var selectedNode = params.nodes[0];
    var degrees = 2;

    // mark all nodes as hard to read.
    for (let nodeId in allNodes) {
      // nodeColors[nodeId] = allNodes[nodeId].color;
      allNodes[nodeId].color = "rgba(200,200,200,0.5)";
      if (allNodes[nodeId].hiddenLabel === undefined) {
        allNodes[nodeId].hiddenLabel = allNodes[nodeId].label;
        allNodes[nodeId].label = undefined;
      }
    }
    var connectedNodes = network.getConnectedNodes(selectedNode);
    var allConnectedNodes = [];

    // get the second degree nodes
    for (i = 1; i < degrees; i++) {
      for (j = 0; j < connectedNodes.length; j++) {
        allConnectedNodes = allConnectedNodes.concat(
          network.getConnectedNodes(connectedNodes[j])
        );
      }
    }

    // all second degree nodes get a different color and their label back
    for (i = 0; i < allConnectedNodes.length; i++) {
      // allNodes[allConnectedNodes[i]].color = "pink";
      allNodes[allConnectedNodes[i]].color = "rgba(150,150,150,0.75)";
      if (allNodes[allConnectedNodes[i]].hiddenLabel !== undefined) {
        allNodes[allConnectedNodes[i]].label =
          allNodes[allConnectedNodes[i]].hiddenLabel;
        allNodes[allConnectedNodes[i]].hiddenLabel = undefined;
      }
    }

    // all first degree nodes get their own color and their label back
    for (i = 0; i < connectedNodes.length; i++) {
      // allNodes[connectedNodes[i]].color = undefined;
      allNodes[connectedNodes[i]].color = nodeColors[connectedNodes[i]];
      if (allNodes[connectedNodes[i]].hiddenLabel !== undefined) {
        allNodes[connectedNodes[i]].label =
          allNodes[connectedNodes[i]].hiddenLabel;
        allNodes[connectedNodes[i]].hiddenLabel = undefined;
      }
    }

    // the main node gets its own color and its label back.
    // allNodes[selectedNode].color = undefined;
    allNodes[selectedNode].color = nodeColors[selectedNode];
    if (allNodes[selectedNode].hiddenLabel !== undefined) {
      allNodes[selectedNode].label = allNodes[selectedNode].hiddenLabel;
      allNodes[selectedNode].hiddenLabel = undefined;
    }
  } else if (highlightActive === true) {
    // console.log("highlightActive was true");
    // reset all nodes
    for (let nodeId in allNodes) {
      // allNodes[nodeId].color = "purple";
      allNodes[nodeId].color = nodeColors[nodeId];
      // delete allNodes[nodeId].color;
      if (allNodes[nodeId].hiddenLabel !== undefined) {
        allNodes[nodeId].label = allNodes[nodeId].hiddenLabel;
        allNodes[nodeId].hiddenLabel = undefined;
      }
    }
    highlightActive = false;
  }

  // transform the object into an array
  var updateArray = [];
  if (params.nodes.length > 0) {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        // console.log(allNodes[nodeId]);
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  } else {
    // console.log("Nothing was selected");
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        // console.log(allNodes[nodeId]);
        // allNodes[nodeId].color = {};
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  }
}

function filterHighlight(params) {
  allNodes = nodes.get({ returnType: "Object" });
  // if something is selected:
  if (params.nodes.length > 0) {
    filterActive = true;
    let selectedNodes = params.nodes;

    // hiding all nodes and saving the label
    for (let nodeId in allNodes) {
      allNodes[nodeId].hidden = true;
      if (allNodes[nodeId].savedLabel === undefined) {
        allNodes[nodeId].savedLabel = allNodes[nodeId].label;
        allNodes[nodeId].label = undefined;
      }
    }

    for (let i=0; i < selectedNodes.length; i++) {
      allNodes[selectedNodes[i]].hidden = false;
      if (allNodes[selectedNodes[i]].savedLabel !== undefined) {
        allNodes[selectedNodes[i]].label = allNodes[selectedNodes[i]].savedLabel;
        allNodes[selectedNodes[i]].savedLabel = undefined;
      }
    }

  } else if (filterActive === true) {
    // reset all nodes
    for (let nodeId in allNodes) {
      allNodes[nodeId].hidden = false;
      if (allNodes[nodeId].savedLabel !== undefined) {
        allNodes[nodeId].label = allNodes[nodeId].savedLabel;
        allNodes[nodeId].savedLabel = undefined;
      }
    }
    filterActive = false;
  }

  // transform the object into an array
  var updateArray = [];
  if (params.nodes.length > 0) {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  } else {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  }
}

function selectNode(nodes) {
  network.selectNodes(nodes);
  neighbourhoodHighlight({ nodes: nodes });
  return nodes;
}

function selectNodes(nodes) {
  network.selectNodes(nodes);
  filterHighlight({nodes: nodes});
  return nodes;
}

function highlightFilter(filter) {
  let selectedNodes = []
  let selectedProp = filter['property']
  if (filter['item'] === 'node') {
    let allNodes = nodes.get({ returnType: "Object" });
    for (let nodeId in allNodes) {
      if (allNodes[nodeId][selectedProp] && filter['value'].includes((allNodes[nodeId][selectedProp]).toString())) {
        selectedNodes.push(nodeId)
      }
    }
  }
  else if (filter['item'] === 'edge'){
    let allEdges = edges.get({returnType: 'object'});
    // check if the selected property exists for selected edge and select the nodes connected to the edge
    for (let edge in allEdges) {
      if (allEdges[edge][selectedProp] && filter['value'].includes((allEdges[edge][selectedProp]).toString())) {
        selectedNodes.push(allEdges[edge]['from'])
        selectedNodes.push(allEdges[edge]['to'])
      }
    }
  }
  selectNodes(selectedNodes)
}</script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css" integrity="sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
            <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
            
            
            
            
            
            

        
<center>
<h1></h1>
</center>

<!-- <link rel="stylesheet" href="../node_modules/vis/dist/vis.min.css" type="text/css" />
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>-->
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/css/bootstrap.min.css"
          rel="stylesheet"
          integrity="sha384-eOJMYsd53ii+scO/bJGFsiCZc+5NDVN2yr8+0RDqr0Ql0h+rP48ckxlpbzKgwra6"
          crossorigin="anonymous"
        />
        <script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>


        <center>
          <h1></h1>
        </center>
        <style type="text/css">

             #mynetwork {
                 width: 100%;
                 height: 900px;
                 background-color: #ffffff;
                 border: 1px solid lightgray;
                 position: relative;
                 float: left;
             }

             
             #loadingBar {
                 position:absolute;
                 top:0px;
                 left:0px;
                 width: 100%;
                 height: 900px;
                 background-color:rgba(200,200,200,0.8);
                 -webkit-transition: all 0.5s ease;
                 -moz-transition: all 0.5s ease;
                 -ms-transition: all 0.5s ease;
                 -o-transition: all 0.5s ease;
                 transition: all 0.5s ease;
                 opacity:1;
             }

             #bar {
                 position:absolute;
                 top:0px;
                 left:0px;
                 width:20px;
                 height:20px;
                 margin:auto auto auto auto;
                 border-radius:11px;
                 border:2px solid rgba(30,30,30,0.05);
                 background: rgb(0, 173, 246); /* Old browsers */
                 box-shadow: 2px 0px 4px rgba(0,0,0,0.4);
             }

             #border {
                 position:absolute;
                 top:10px;
                 left:10px;
                 width:500px;
                 height:23px;
                 margin:auto auto auto auto;
                 box-shadow: 0px 0px 4px rgba(0,0,0,0.2);
                 border-radius:10px;
             }

             #text {
                 position:absolute;
                 top:8px;
                 left:530px;
                 width:30px;
                 height:50px;
                 margin:auto auto auto auto;
                 font-size:22px;
                 color: #000000;
             }

             div.outerBorder {
                 position:relative;
                 top:400px;
                 width:600px;
                 height:44px;
                 margin:auto auto auto auto;
                 border:8px solid rgba(0,0,0,0.1);
                 background: rgb(252,252,252); /* Old browsers */
                 background: -moz-linear-gradient(top,  rgba(252,252,252,1) 0%, rgba(237,237,237,1) 100%); /* FF3.6+ */
                 background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(252,252,252,1)), color-stop(100%,rgba(237,237,237,1))); /* Chrome,Safari4+ */
                 background: -webkit-linear-gradient(top,  rgba(252,252,252,1) 0%,rgba(237,237,237,1) 100%); /* Chrome10+,Safari5.1+ */
                 background: -o-linear-gradient(top,  rgba(252,252,252,1) 0%,rgba(237,237,237,1) 100%); /* Opera 11.10+ */
                 background: -ms-linear-gradient(top,  rgba(252,252,252,1) 0%,rgba(237,237,237,1) 100%); /* IE10+ */
                 background: linear-gradient(to bottom,  rgba(252,252,252,1) 0%,rgba(237,237,237,1) 100%); /* W3C */
                 filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#fcfcfc', endColorstr='#ededed',GradientType=0 ); /* IE6-9 */
                 border-radius:72px;
                 box-shadow: 0px 0px 10px rgba(0,0,0,0.2);
             }
             

             
             #config {
                 float: left;
                 width: 400px;
                 height: 600px;
             }
             

             
        </style>
    </head>


    <body>
        <div class="card" style="width: 100%">
            
            
            <div id="mynetwork" class="card-body"></div>
        </div>

        
            <div id="loadingBar">
              <div class="outerBorder">
                <div id="text">0%</div>
                <div id="border">
                  <div id="bar"></div>
                </div>
              </div>
            </div>
        
        
            <div id="config"></div>
        

        <script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "gold", "id": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "label": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "node_class": "Document", "shape": "star", "size": 25}, {"color": "lightgreen", "id": "N18", "label": "SENT_1", "node_class": "SentenceNode", "shape": "box", "size": 20, "title": "Abstract Liver enzymes are commonly used in the evaluation of patients with a\nrange of diseases. Classically Liver enzymes are used to give information on\nwhether a patient\u2019s primary disorder is hepatitic or cholestatic in origin.\nHowever, knowledge of enzyme ratios and pattern recognition allow much more\ninformation to be derived from Liver enzymes. This paper offers an insight to\ngeneralists on how to extract greater information from Liver enzymes in order to\nimprove the investigation and management of liver disease. Introduction Liver\nFunction Tests (LFTs) are one of the most commonlyrequested screening blood\ntests."}, {"color": "lightgreen", "id": "N32", "label": "SENT_2", "node_class": "SentenceNode", "shape": "box", "size": 20, "title": "Whether for the investigation of suspected liver disease, monitoring of disease\nactivity, or simply as \u2018routine\u2019 blood analysis, Liver Function Tests (LFTs) can\nprovide a host of information on a range of disease processes. The title \u2018liver\nfunction tests\u2019 is, however, somewhat of a misnomer; only the bilirubin and\nalbumin given in this panel offer information regarding the functional capacity\nof the liver. At a basic level the evaluation of liver enzymes simply gives\ninformation as to whether a patient\u2019s primary disorder is hepatitic or\ncholestatic in origin. However, much more may be interpreted from these assays\nwith knowledge of enzyme ratios and pattern recognition."}, {"color": "lightgreen", "id": "N33", "label": "SENT_3", "node_class": "SentenceNode", "shape": "box", "size": 20, "title": "This paper offers an insight to generalists of how to yield greater information\nfrom this simple test. Patterns and Use of Hepatic Enzymes in Practice The liver\nenzyme profile should always be assessed in conjunction with a thorough history\nand clinical examination. Despite a thorough history and clinical examination,\nthere are many occasions when doubt persists over an underlying diagnosis. For\nexample, does an overweight diabetic who enjoys a few glasses of wine at the\nweekend have alcoholic or non-alcoholic fatty liver disease? In such\ncircumstances the absolute liver enzyme levels and ratios may point the\nclinician in the right direction. Furthermore, the pattern of enzymes will\nassist, not only with differentiating between cholestasis and hepatitis, but\nwill aid diagnosis when there is a mixed picture."}, {"color": "lightgreen", "id": "N50", "label": "SENT_4", "node_class": "SentenceNode", "shape": "box", "size": 20, "title": "Understanding Cholestasis: Mechanical or Medical? Mechanical biliary obstruction\nresults in raised levels of ALP,GGT and often bilirubin. ALP will usually be\nmarkedly raised in comparison with ALT. Levels of ALP and GGT elevated in\nsimilar proportions signify a hepatobiliary source. Otherwise alternative causes\nof single enzyme elevation should be considered. When due to\ncholedocholithiasis, the levels of ALP and GGT tend to fluctuate (in comparison\nto stricture forming disease) and may be associated with a normal bilirubin.\nEnzyme titres tend to rise and fall gradually and may be preceded by a peaked\nrise in liver transaminases which can reach \u003e1000 I/U."}, {"color": "lightgreen", "id": "N66", "label": "SENT_5", "node_class": "SentenceNode", "shape": "box", "size": 20, "title": "The AST:ALT ratio (De Ritis ratio) may assist in differentiating the site of\nbiliary obstruction. When associated with a cholestatic picture, an AST:ALT\nratio of \u003c1.5 suggests an extrahepatic obstruction. In such circumstances the\nALT titre is frequently considerably higher than AST. An AST:ALT ratio of \u003e1.5\nindicates intrahepatic (mechanical or medical) cholestasis is more likely. Drug-\ninduced cholestasis usually presents with a preferential rise in ALP, rather\nthan GGT, or with an ALT:ALP ratio of \u003c2. Causative drugs would include:\nantibiotics, immunosuppressants, tricyclic antidepressants and angiotensin\nconverting enzyme inhibitors."}, {"color": "lightgreen", "id": "N112", "label": "SENT_6", "node_class": "SentenceNode", "shape": "box", "size": 20, "title": "In Primary Biliary Cirrhosis, an autoimmune condition of the intrahepatic\nbiliary ducts, the level of ALP is generally greater than that of GGT. In this\ncase, transaminases are invariably normal or only minimally elevated. Both the\nEuropean Association for Study of the Liver (EASL) and the American Association\nfor Study of Liver Disease (AASLD) recommend that a diagnosis of PBC may be\nbased on cholestatic liver enzyme levels in conjunction with the demonstration\nof antimitochondrial antibodies. If either of these two criteria is absent,\nimaging and liver biopsy become necessary."}, {"color": "lightgreen", "id": "N113", "label": "SENT_7", "node_class": "SentenceNode", "shape": "box", "size": 20, "title": "AST and ALP are used within some scoring criteria to monitor the effects of\nursodeoxycholic acid in the management of PBC. A recent study has shown that a\nraised AST:ALT ratio outperforms other non-histological indicators of cirrhosis\nin PBC, but still only achieves a low sensitivity and a specificity of 65-79%.\nAs with PBC, liver enzymes play a key role in the diagnosis of Primary\nSclerosing Cholangitis (PSC)."}, {"color": "lightgreen", "id": "N130", "label": "SENT_8", "node_class": "SentenceNode", "shape": "box", "size": 20, "title": "When other causes of liver disease have been excluded, a raised GGT, and\nparticularly ALP, are diagnostic when associated with typical Endoscopic\nRetrograde Cholangiopancreatography (ERCP) or Magnetic Resonance\nCholangiopancreatography (MRCP) findings. This can preclude the need for a liver\nbiopsy. Transaminase levels may be raised up to 2-3 times normal values in PSC\nbut this is not diagnostic. AST is a component of the Mayo Risk Score, which\ncalculates the risk of disease progression in PSC. A high Mayo Risk Score, and\nan AST:ALT ratio of \u003e1.12 have been shown to be indicators of risk for the\ndevelopment of oesophageal varices."}, {"color": "lightgreen", "id": "N155", "label": "SENT_9", "node_class": "SentenceNode", "shape": "box", "size": 20, "title": "In Primary Sclerosing Cholangitis (PSC), as with other liver diseases, there are\nsuggestions that an AST:ALT ratio of \u003e1 indicates the development of cirrhosis.\nAlcohol induces hepatic enzymes leading to a raised GGT with an ALP which may be\nnormal, or disproportionately lower than the GGT. A GGT:ALP ratio \u003e2.5 in\nassociation with jaundice suggests alcohol as a cause of liver disease. The\npresence of a macrocytosis, due to either an associated dietary deficiency of\nfolate or B12, or due to a direct suppression of bone marrow by alcohol is\nsupportive of the diagnosis of alcoholic liver disease."}, {"color": "lightgreen", "id": "N156", "label": "SENT_10", "node_class": "SentenceNode", "shape": "box", "size": 20, "title": "A raised GGT is not diagnostic of alcohol abuse, with research showing A raised\nGGT remains high in former drinkers as well as current drinkers. In men, the\nhighest levels of GGT occur in those who drink daily. In women, binge drinkers\nand those consuming alcohol without food will have especially high levels. The\nlevel of GGT is loosely dose dependant, with those in the top two quartiles of\nalcohol intake having the highest titres."}, {"color": "lightgreen", "id": "N170", "label": "SENT_11", "node_class": "SentenceNode", "shape": "box", "size": 20, "title": "The Fatty Liver and the AST:ALT Ratio During the last few decades there has been\nresearch into using the AST:ALT ratio in the differentiation of alcoholic liver\ndisease (ALD) from other forms of liver disease, particularly the Non-alcoholic\nFatty Liver Disease (NAFLD) spectrum. Both AST and ALT enzymes require\npyridoxal-5\u2019-phosphate (vitamin B6) to function properly. pyridoxal-5\u2019-phosphate\n(vitamin B6)\u0027s absence in nutritionally-deficient heavy-drinkers has a much\nlarger effect on the production of ALT than that of AST, causing the AST:ALT\nratio to rise. A normal AST:ALT ratio should be \u003c1."}, {"color": "lightgreen", "id": "N179", "label": "SENT_12", "node_class": "SentenceNode", "shape": "box", "size": 20, "title": "In patients with alcoholic liver disease, the AST:ALT ratio is \u003e1 in 92% of\npatients, and \u003e2 in 70%.13 AST:ALT scores \u003e2 are, therefore, strongly suggestive\nof alcoholic liver disease and scores \u003c1 more suggestive of the Non-alcoholic\nFatty Liver Disease (NAFLD) spectrum. High ratios reflect the severity of\nhepatitis or underlying liver disease rather than high alcohol consumption. This\nmeans that most heavy-drinkers will not have an AST: ALT ratio \u003e1 as most heavy-\ndrinkers have not yet developed alcoholic liver disease (ALD)."}, {"color": "lightgreen", "id": "N190", "label": "SENT_13", "node_class": "SentenceNode", "shape": "box", "size": 20, "title": "No studies have shown that the AST:ALT ratio, either alone or in combination\nwith other factors or models, has the necessary sensitivity or specificity to\ndefinitively differentiate between alcoholic liver disease (ALD) and NAFLD, but\nthe AST:ALT ratio acts as a useful clinical guide when considering the need for\nliver biopsy. It should also be noted that liver transaminases are known to\nworsen in response to cessation of alcohol intake (often coinciding with\nadmission to hospital) and that ALT has also been shown to rise simply from\nadmission to hospital, even in patients with no liver disease."}, {"color": "lightgreen", "id": "N198", "label": "SENT_14", "node_class": "SentenceNode", "shape": "box", "size": 20, "title": "Although models exist which exclude cirrhosis in NAFLD with reasonable accuracy,\nliver enzyme analysis has so far failed to provide a sensitive and specific\nenough means to make a diagnosis. At present liver biopsy cannot be avoided in\ncases where confirmation of NASH or cirrhosis is necessary. The role of liver\nenzyme analysis in NAFLD lies in both the early identification and modification\nof associated metabolic risk factors such as hypertension, hyperlipidaemia and\nglycaemic control and in risk stratification for the future."}, {"color": "lightgreen", "id": "N229", "label": "SENT_15", "node_class": "SentenceNode", "shape": "box", "size": 20, "title": "A scoring system developed at the Mayo clinic uses age, hyperglycemia, body mass\nindex, platelet count, albumin, and AST:ALT ratio to accurately differentiate\npatients with advanced fibrosis in NAFLD. the AST:ALT Ratio becomes considerably\nless specific in determining underlying disease with the development of\ncirrhosis, as the AST:ALT Ratio will increase across a broad range of diseases.\nthe AST:ALT Ratio is, however, useful in NAFLD patients known not to be abusing\nalcohol as a score of \u003e1 should lead to the consideration that NAFLD patients\nknown not to be abusing alcohol may have developed cirrhosis."}, {"color": "lightcoral", "id": "L1", "label": "L1", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L1\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N125", "label": "liver enzymes", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: liver enzymes\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N3", "label": "Patient", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Patient\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N4", "label": "Disease", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Disease\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N2", "label": "used in evaluation of", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: used in evaluation of\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L2", "label": "L2", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L2\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N6", "label": "Primary disorder", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Primary disorder\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N25", "label": "Hepatitic disorder", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Hepatitic disorder\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N26", "label": "Cholestatic disorder", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Cholestatic disorder\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N5", "label": "provide information on", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: provide information on\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L3", "label": "L3", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L3\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N9", "label": "Enzyme ratios", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Enzyme ratios\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N10", "label": "Pattern recognition", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Pattern recognition\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N11", "label": "extract information from", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: extract information from\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L4", "label": "L4", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L4\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N180", "label": "Underlying liver disease", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Underlying liver disease\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N12", "label": "improve investigation of", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: improve investigation of\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L5", "label": "L5", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L5\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N13", "label": "improve management of", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: improve management of\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L6", "label": "L6", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L6\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N15", "label": "Liver Function Tests", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Liver Function Tests\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N17", "label": "Blood test", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Blood test\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N16", "label": "used as screening for", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: used as screening for\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L7", "label": "L7", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L7\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L8", "label": "L8", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L8\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L9", "label": "L9", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L9\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L10", "label": "L10", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L10\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L11", "label": "L11", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L11\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L12", "label": "L12", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L12\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L13", "label": "L13", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L13\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N27", "label": "provides information on", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: provides information on\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L14", "label": "L14", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L14\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N23", "label": "Disease activity", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Disease activity\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L15", "label": "L15", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L15\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N24", "label": "Blood analysis", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Blood analysis\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L16", "label": "L16", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L16\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N20", "label": "Bilirubin", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Bilirubin\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N22", "label": "Liver functional capacity", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Liver functional capacity\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N28", "label": "offers information on", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: offers information on\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L17", "label": "L17", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L17\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N21", "label": "Albumin", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Albumin\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L18", "label": "L18", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L18\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N30", "label": "indicates origin", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: indicates origin\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L19", "label": "L19", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L19\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N31", "label": "interpreted by", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: interpreted by\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L20", "label": "L20", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L20\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L21", "label": "L21", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L21\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L22", "label": "L22", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L22\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L23", "label": "L23", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L23\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L24", "label": "L24", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L24\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L25", "label": "L25", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L25\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L26", "label": "L26", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L26\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L27", "label": "L27", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L27\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N35", "label": "Liver enzyme profile", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Liver enzyme profile\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N36", "label": "History and clinical examination", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: History and clinical examination\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N49", "label": "should_be_assessed_with", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: should_be_assessed_with\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L28", "label": "L28", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L28\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N44", "label": "Absolute liver enzyme levels and ratios", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Absolute liver enzyme levels and ratios\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N42", "label": "Diagnosis", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Diagnosis\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N48", "label": "points", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: points\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L29", "label": "L29", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L29\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N43", "label": "Pattern of hepatic enzymes", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Pattern of hepatic enzymes\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N39", "label": "Cholestasis", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Cholestasis\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N40", "label": "Hepatitis", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Hepatitis\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N45", "label": "differentiates", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: differentiates\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L30", "label": "L30", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L30\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N41", "label": "Mixed liver disease picture", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Mixed liver disease picture\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N47", "label": "aids", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: aids\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L31", "label": "L31", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L31\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L32", "label": "L32", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L32\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L33", "label": "L33", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L33\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L34", "label": "L34", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L34\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L35", "label": "L35", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L35\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N51", "label": "Mechanical biliary obstruction", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Mechanical biliary obstruction\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N80", "label": "ALP", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: ALP\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N60", "label": "increases", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: increases\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L36", "label": "L36", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L36\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N97", "label": "Gamma-glutamyl transferase", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Gamma-glutamyl transferase\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L37", "label": "L37", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L37\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L38", "label": "L38", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L38\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N54", "label": "Hepatobiliary source", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Hepatobiliary source\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N62", "label": "signifies", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: signifies\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L39", "label": "L39", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L39\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N52", "label": "Choledocholithiasis", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Choledocholithiasis\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N61", "label": "fluctuates", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: fluctuates\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L40", "label": "L40", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L40\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L41", "label": "L41", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L41\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N59", "label": "Enzyme titres", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Enzyme titres\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L42", "label": "L42", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L42\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N58", "label": "Liver transaminases", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Liver transaminases\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N64", "label": "peaked rise", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: peaked rise\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L43", "label": "L43", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L43\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N177", "label": "monitoring", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: monitoring\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N63", "label": "requires", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: requires\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L44", "label": "L44", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L44\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L45", "label": "L45", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L45\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L46", "label": "L46", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L46\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L47", "label": "L47", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L47\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L48", "label": "L48", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L48\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L49", "label": "L49", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L49\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L50", "label": "L50", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L50\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L51", "label": "L51", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L51\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L52", "label": "L52", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L52\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L53", "label": "L53", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L53\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L54", "label": "L54", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L54\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L55", "label": "L55", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L55\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L56", "label": "L56", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L56\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L57", "label": "L57", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L57\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N67", "label": "AST:ALT ratio", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: AST:ALT ratio\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N69", "label": "Biliary obstruction", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Biliary obstruction\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N46", "label": "assists", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: assists\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L58", "label": "L58", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L58\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N71", "label": "\u003c1.5", "node_class": "NumberNode", "shape": "ellipse", "size": 15, "title": "Atom: \u003c1.5\u003cbr\u003eType: NumberNode"}, {"color": "skyblue", "id": "N70", "label": "Cholestatic picture", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Cholestatic picture\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N72", "label": "Extrahepatic obstruction", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Extrahepatic obstruction\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N87", "label": "suggests", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: suggests\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L59", "label": "L59", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L59\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N73", "label": "ALT", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: ALT\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N74", "label": "AST", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: AST\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N91", "label": "higher_than", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: higher_than\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L60", "label": "L60", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L60\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N75", "label": "\u003e1.5", "node_class": "NumberNode", "shape": "ellipse", "size": 15, "title": "Atom: \u003e1.5\u003cbr\u003eType: NumberNode"}, {"color": "skyblue", "id": "N76", "label": "Intrahepatic cholestasis", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Intrahepatic cholestasis\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N88", "label": "indicates", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: indicates\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L61", "label": "L61", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L61\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N77", "label": "Mechanical cholestasis", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Mechanical cholestasis\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L62", "label": "L62", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L62\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N78", "label": "Medical cholestasis", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Medical cholestasis\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L63", "label": "L63", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L63\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N79", "label": "Drug-induced cholestasis", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Drug-induced cholestasis\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N92", "label": "rise", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: rise\u003cbr\u003eType: PredicateNode"}, {"color": "skyblue", "id": "N89", "label": "presents_with", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: presents_with\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L64", "label": "L64", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L64\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N82", "label": "\u003c2", "node_class": "NumberNode", "shape": "ellipse", "size": 15, "title": "Atom: \u003c2\u003cbr\u003eType: NumberNode"}, {"color": "lightcoral", "id": "L65", "label": "L65", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L65\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N83", "label": "Antibiotics", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Antibiotics\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N90", "label": "causes", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: causes\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L66", "label": "L66", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L66\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N84", "label": "Immunosuppressants", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Immunosuppressants\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L67", "label": "L67", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L67\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N85", "label": "Tricyclic antidepressants", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Tricyclic antidepressants\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L68", "label": "L68", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L68\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N86", "label": "Angiotensin converting enzyme inhibitors", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Angiotensin converting enzyme inhibitors\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L69", "label": "L69", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L69\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L70", "label": "L70", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L70\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L71", "label": "L71", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L71\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L72", "label": "L72", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L72\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N81", "label": "GGT", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: GGT\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L73", "label": "L73", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L73\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L74", "label": "L74", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L74\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L75", "label": "L75", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L75\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L76", "label": "L76", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L76\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L77", "label": "L77", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L77\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L78", "label": "L78", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L78\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L79", "label": "L79", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L79\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L80", "label": "L80", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L80\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L81", "label": "L81", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L81\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L82", "label": "L82", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L82\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L83", "label": "L83", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L83\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L84", "label": "L84", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L84\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L85", "label": "L85", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L85\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L86", "label": "L86", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L86\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L87", "label": "L87", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L87\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L88", "label": "L88", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L88\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L89", "label": "L89", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L89\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N115", "label": "Primary Biliary Cholangitis", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Primary Biliary Cholangitis\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N94", "label": "Autoimmune condition", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Autoimmune condition\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L90", "label": "L90", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L90\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N95", "label": "Intrahepatic biliary ducts", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Intrahepatic biliary ducts\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L91", "label": "L91", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L91\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N106", "label": "greater_than", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: greater_than\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L92", "label": "L92", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L92\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N98", "label": "Transaminases", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Transaminases\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N107", "label": "normal", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: normal\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L93", "label": "L93", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L93\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N108", "label": "minimally_elevated", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: minimally_elevated\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L94", "label": "L94", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L94\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N104", "label": "European Association for Study of the Liver", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: European Association for Study of the Liver\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N105", "label": "American Association for Study of Liver Disease", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: American Association for Study of Liver Disease\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N99", "label": "Cholestatic liver enzyme levels", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Cholestatic liver enzyme levels\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N100", "label": "Antimitochondrial antibodies", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Antimitochondrial antibodies\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N109", "label": "recommend", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: recommend\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L95", "label": "L95", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L95\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N110", "label": "based_on", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: based_on\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L96", "label": "L96", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L96\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N111", "label": "conjunction_with", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: conjunction_with\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L97", "label": "L97", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L97\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N102", "label": "Imaging", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Imaging\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N103", "label": "Liver biopsy", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Liver biopsy\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L98", "label": "L98", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L98\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L99", "label": "L99", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L99\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L100", "label": "L100", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L100\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L101", "label": "L101", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L101\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L102", "label": "L102", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L102\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L103", "label": "L103", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L103\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L104", "label": "L104", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L104\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L105", "label": "L105", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L105\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L106", "label": "L106", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L106\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L107", "label": "L107", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L107\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N118", "label": "scoring criteria", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: scoring criteria\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N117", "label": "effects", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: effects\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N114", "label": "Ursodeoxycholic acid", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Ursodeoxycholic acid\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N116", "label": "monitor", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: monitor\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L108", "label": "L108", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L108\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N119", "label": "raised AST:ALT ratio", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: raised AST:ALT ratio\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N121", "label": "non-histological indicators of cirrhosis", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: non-histological indicators of cirrhosis\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N120", "label": "outperforms", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: outperforms\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L109", "label": "L109", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L109\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L110", "label": "L110", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L110\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N126", "label": "Primary Sclerosing Cholangitis", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Primary Sclerosing Cholangitis\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N128", "label": "key role", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: key role\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L111", "label": "L111", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L111\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L112", "label": "L112", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L112\u003cbr\u003eType: ContextLink"}, {"color": "skyblue", "id": "N129", "label": "recent study", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: recent study\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L113", "label": "L113", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L113\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L114", "label": "L114", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L114\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L115", "label": "L115", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L115\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N134", "label": "Endoscopic Retrograde Cholangiopancreatography", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Endoscopic Retrograde Cholangiopancreatography\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N135", "label": "Magnetic Resonance Cholangiopancreatography", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Magnetic Resonance Cholangiopancreatography\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N136", "label": "diagnostic", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: diagnostic\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L116", "label": "L116", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L116\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N137", "label": "precludes", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: precludes\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L117", "label": "L117", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L117\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N131", "label": "Transaminase", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Transaminase\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N141", "label": "raised", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: raised\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L118", "label": "L118", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L118\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L119", "label": "L119", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L119\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N132", "label": "Mayo Risk Score", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Mayo Risk Score\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N138", "label": "component_of", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: component_of\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L120", "label": "L120", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L120\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N139", "label": "calculates", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: calculates\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L121", "label": "L121", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L121\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N133", "label": "Oesophageal varices", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Oesophageal varices\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N140", "label": "indicator_of", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: indicator_of\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L122", "label": "L122", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L122\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L123", "label": "L123", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L123\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L124", "label": "L124", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L124\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L125", "label": "L125", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L125\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L126", "label": "L126", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L126\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L127", "label": "L127", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L127\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L128", "label": "L128", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L128\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L129", "label": "L129", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L129\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L130", "label": "L130", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L130\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L131", "label": "L131", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L131\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N143", "label": "\u003e1", "node_class": "NumberNode", "shape": "ellipse", "size": 15, "title": "Atom: \u003e1\u003cbr\u003eType: NumberNode"}, {"color": "skyblue", "id": "N144", "label": "Cirrhosis", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Cirrhosis\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L132", "label": "L132", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L132\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N145", "label": "Alcohol", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Alcohol\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N34", "label": "Hepatic enzymes", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Hepatic enzymes\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L133", "label": "L133", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L133\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L134", "label": "L134", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L134\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N146", "label": "GGT:ALP ratio", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: GGT:ALP ratio\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N147", "label": "\u003e2.5", "node_class": "NumberNode", "shape": "ellipse", "size": 15, "title": "Atom: \u003e2.5\u003cbr\u003eType: NumberNode"}, {"color": "skyblue", "id": "N148", "label": "Jaundice", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Jaundice\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N14", "label": "Liver disease", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Liver disease\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L135", "label": "L135", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L135\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N149", "label": "Macrocytosis", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Macrocytosis\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L136", "label": "L136", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L136\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N150", "label": "Folate deficiency", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Folate deficiency\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L137", "label": "L137", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L137\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N151", "label": "B12 deficiency", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: B12 deficiency\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L138", "label": "L138", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L138\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N152", "label": "Bone marrow suppression", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Bone marrow suppression\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L139", "label": "L139", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L139\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N153", "label": "Alcoholic liver disease", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Alcoholic liver disease\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N154", "label": "supports", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: supports\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L140", "label": "L140", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L140\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L141", "label": "L141", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L141\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L142", "label": "L142", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L142\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L143", "label": "L143", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L143\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L144", "label": "L144", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L144\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L145", "label": "L145", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L145\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L146", "label": "L146", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L146\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L147", "label": "L147", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L147\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L148", "label": "L148", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L148\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L149", "label": "L149", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L149\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N157", "label": "Alcohol abuse", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Alcohol abuse\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N169", "label": "not diagnostic of", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: not diagnostic of\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L150", "label": "L150", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L150\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N167", "label": "GGT level", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: GGT level\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N158", "label": "Former drinkers", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Former drinkers\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N168", "label": "high", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: high\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L151", "label": "L151", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L151\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N159", "label": "Current drinkers", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Current drinkers\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L152", "label": "L152", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L152\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N160", "label": "Men", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Men\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N161", "label": "Daily drinkers", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Daily drinkers\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L153", "label": "L153", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L153\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N162", "label": "Women", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Women\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N163", "label": "Binge drinkers", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Binge drinkers\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L154", "label": "L154", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L154\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N164", "label": "Alcohol consumption without food", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Alcohol consumption without food\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L155", "label": "L155", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L155\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N165", "label": "Alcohol intake", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Alcohol intake\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L156", "label": "L156", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L156\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N166", "label": "Top two quartiles of alcohol intake", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Top two quartiles of alcohol intake\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L157", "label": "L157", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L157\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L158", "label": "L158", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L158\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L159", "label": "L159", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L159\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L160", "label": "L160", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L160\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L161", "label": "L161", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L161\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L162", "label": "L162", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L162\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L163", "label": "L163", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L163\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L164", "label": "L164", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L164\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L165", "label": "L165", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L165\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L166", "label": "L166", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L166\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L167", "label": "L167", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L167\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L168", "label": "L168", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L168\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L169", "label": "L169", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L169\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L170", "label": "L170", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L170\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N193", "label": "NAFLD", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: NAFLD\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L171", "label": "L171", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L171\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N174", "label": "pyridoxal-5\u2019-phosphate (vitamin B6)", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: pyridoxal-5\u2019-phosphate (vitamin B6)\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L172", "label": "L172", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L172\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L173", "label": "L173", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L173\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N181", "label": "Heavy drinker", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Heavy drinker\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N176", "label": "production", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: production\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L174", "label": "L174", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L174\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L175", "label": "L175", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L175\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L176", "label": "L176", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L176\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N178", "label": "\u003c1", "node_class": "NumberNode", "shape": "ellipse", "size": 15, "title": "Atom: \u003c1\u003cbr\u003eType: NumberNode"}, {"color": "lightcoral", "id": "L177", "label": "L177", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L177\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L178", "label": "L178", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L178\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L179", "label": "L179", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L179\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L180", "label": "L180", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L180\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L181", "label": "L181", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L181\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L182", "label": "L182", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L182\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L183", "label": "L183", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L183\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L184", "label": "L184", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L184\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L185", "label": "L185", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L185\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N187", "label": "suggestive_of", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: suggestive_of\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L186", "label": "L186", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L186\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N184", "label": "\u003e2", "node_class": "NumberNode", "shape": "ellipse", "size": 15, "title": "Atom: \u003e2\u003cbr\u003eType: NumberNode"}, {"color": "lightcoral", "id": "L187", "label": "L187", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L187\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L188", "label": "L188", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L188\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N182", "label": "Severity", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Severity\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N188", "label": "reflects", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: reflects\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L189", "label": "L189", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L189\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N183", "label": "High alcohol consumption", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: High alcohol consumption\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L190", "label": "L190", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L190\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L191", "label": "L191", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L191\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N189", "label": "developed", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: developed\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L192", "label": "L192", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L192\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L193", "label": "L193", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L193\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L194", "label": "L194", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L194\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L195", "label": "L195", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L195\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L196", "label": "L196", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L196\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L197", "label": "L197", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L197\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L198", "label": "L198", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L198\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L199", "label": "L199", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L199\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L200", "label": "L200", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L200\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L201", "label": "L201", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L201\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L202", "label": "L202", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L202\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N196", "label": "Cessation of alcohol intake", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Cessation of alcohol intake\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N197", "label": "Admission to hospital", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Admission to hospital\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N195", "label": "worsen", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: worsen\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L203", "label": "L203", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L203\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L204", "label": "L204", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L204\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L205", "label": "L205", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L205\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L206", "label": "L206", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L206\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L207", "label": "L207", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L207\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N208", "label": "excludes", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: excludes\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L208", "label": "L208", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L208\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N200", "label": "Liver enzyme analysis", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Liver enzyme analysis\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N191", "label": "Sensitivity", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Sensitivity\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N192", "label": "Specificity", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Specificity\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N209", "label": "fails", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: fails\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L209", "label": "L209", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L209\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N199", "label": "Non-alcoholic steatohepatitis", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Non-alcoholic steatohepatitis\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L210", "label": "L210", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L210\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L211", "label": "L211", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L211\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N206", "label": "Early identification", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Early identification\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N201", "label": "Metabolic risk factors", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Metabolic risk factors\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N213", "label": "identifies", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: identifies\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L212", "label": "L212", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L212\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N207", "label": "Modification", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Modification\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N212", "label": "modifies", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: modifies\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L213", "label": "L213", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L213\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N205", "label": "Risk stratification", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Risk stratification\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N214", "label": "stratifies", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: stratifies\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L214", "label": "L214", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L214\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N202", "label": "Hypertension", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Hypertension\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L215", "label": "L215", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L215\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N203", "label": "Hyperlipidaemia", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Hyperlipidaemia\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L216", "label": "L216", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L216\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N204", "label": "Glycaemic control", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Glycaemic control\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L217", "label": "L217", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L217\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L218", "label": "L218", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L218\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L219", "label": "L219", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L219\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L220", "label": "L220", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L220\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L221", "label": "L221", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L221\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L222", "label": "L222", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L222\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L223", "label": "L223", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L223\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L224", "label": "L224", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L224\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L225", "label": "L225", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L225\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L226", "label": "L226", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L226\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L227", "label": "L227", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L227\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N215", "label": "Mayo Clinic NAFLD Fibrosis Scoring System", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Mayo Clinic NAFLD Fibrosis Scoring System\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N217", "label": "Age", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Age\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N218", "label": "Hyperglycemia", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Hyperglycemia\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N219", "label": "Body Mass Index", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Body Mass Index\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N220", "label": "Platelet Count", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Platelet Count\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N221", "label": "AST:ALT Ratio", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: AST:ALT Ratio\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N226", "label": "uses", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: uses\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L228", "label": "L228", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L228\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N222", "label": "Advanced Fibrosis", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Advanced Fibrosis\u003cbr\u003eType: ConceptNode"}, {"color": "lightcoral", "id": "L229", "label": "L229", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L229\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N223", "label": "Underlying Liver Disease", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Underlying Liver Disease\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N227", "label": "less specific", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: less specific\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L230", "label": "L230", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L230\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L231", "label": "L231", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L231\u003cbr\u003eType: EvaluationLink"}, {"color": "skyblue", "id": "N225", "label": "AST:ALT Ratio \u003e 1", "node_class": "NumberNode", "shape": "ellipse", "size": 15, "title": "Atom: AST:ALT Ratio \u003e 1\u003cbr\u003eType: NumberNode"}, {"color": "skyblue", "id": "N224", "label": "Alcohol Abuse", "node_class": "ConceptNode", "shape": "ellipse", "size": 15, "title": "Atom: Alcohol Abuse\u003cbr\u003eType: ConceptNode"}, {"color": "skyblue", "id": "N228", "label": "consider", "node_class": "PredicateNode", "shape": "ellipse", "size": 15, "title": "Atom: consider\u003cbr\u003eType: PredicateNode"}, {"color": "lightcoral", "id": "L232", "label": "L232", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L232\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L233", "label": "L233", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L233\u003cbr\u003eType: EvaluationLink"}, {"color": "lightcoral", "id": "L234", "label": "L234", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L234\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L235", "label": "L235", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L235\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L236", "label": "L236", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L236\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L237", "label": "L237", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L237\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L238", "label": "L238", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L238\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L239", "label": "L239", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L239\u003cbr\u003eType: ContextLink"}, {"color": "lightcoral", "id": "L240", "label": "L240", "node_class": "Link", "shape": "square", "size": 10, "title": "Link: L240\u003cbr\u003eType: ContextLink"}]);
                  edges = new vis.DataSet([{"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "N18", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "N32", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "N33", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "N50", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "N66", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "N112", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "N113", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "N130", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "N155", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "N156", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "N170", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "N179", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "N190", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "N198", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "N229", "width": 1}, {"arrows": "to", "from": "L1", "title": "EvaluationLink", "to": "N125", "width": 1}, {"arrows": "to", "from": "L1", "title": "EvaluationLink", "to": "N3", "width": 1}, {"arrows": "to", "from": "L1", "title": "EvaluationLink", "to": "N4", "width": 1}, {"arrows": "to", "from": "L1", "title": "EvaluationLink", "to": "N2", "width": 1}, {"arrows": "to", "from": "L2", "title": "EvaluationLink", "to": "N125", "width": 1}, {"arrows": "to", "from": "L2", "title": "EvaluationLink", "to": "N6", "width": 1}, {"arrows": "to", "from": "L2", "title": "EvaluationLink", "to": "N25", "width": 1}, {"arrows": "to", "from": "L2", "title": "EvaluationLink", "to": "N26", "width": 1}, {"arrows": "to", "from": "L2", "title": "EvaluationLink", "to": "N5", "width": 1}, {"arrows": "to", "from": "L3", "title": "EvaluationLink", "to": "N9", "width": 1}, {"arrows": "to", "from": "L3", "title": "EvaluationLink", "to": "N10", "width": 1}, {"arrows": "to", "from": "L3", "title": "EvaluationLink", "to": "N125", "width": 1}, {"arrows": "to", "from": "L3", "title": "EvaluationLink", "to": "N11", "width": 1}, {"arrows": "to", "from": "L4", "title": "EvaluationLink", "to": "N125", "width": 1}, {"arrows": "to", "from": "L4", "title": "EvaluationLink", "to": "N180", "width": 1}, {"arrows": "to", "from": "L4", "title": "EvaluationLink", "to": "N12", "width": 1}, {"arrows": "to", "from": "L5", "title": "EvaluationLink", "to": "N125", "width": 1}, {"arrows": "to", "from": "L5", "title": "EvaluationLink", "to": "N180", "width": 1}, {"arrows": "to", "from": "L5", "title": "EvaluationLink", "to": "N13", "width": 1}, {"arrows": "to", "from": "L6", "title": "EvaluationLink", "to": "N15", "width": 1}, {"arrows": "to", "from": "L6", "title": "EvaluationLink", "to": "N17", "width": 1}, {"arrows": "to", "from": "L6", "title": "EvaluationLink", "to": "N16", "width": 1}, {"arrows": "to", "from": "L7", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L7", "title": "ContextLink", "to": "L1", "width": 1}, {"arrows": "to", "from": "L8", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L8", "title": "ContextLink", "to": "L2", "width": 1}, {"arrows": "to", "from": "L9", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L9", "title": "ContextLink", "to": "L3", "width": 1}, {"arrows": "to", "from": "L10", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L10", "title": "ContextLink", "to": "L4", "width": 1}, {"arrows": "to", "from": "L11", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L11", "title": "ContextLink", "to": "L5", "width": 1}, {"arrows": "to", "from": "L12", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L12", "title": "ContextLink", "to": "L6", "width": 1}, {"arrows": "to", "from": "L13", "title": "EvaluationLink", "to": "N15", "width": 1}, {"arrows": "to", "from": "L13", "title": "EvaluationLink", "to": "N180", "width": 1}, {"arrows": "to", "from": "L13", "title": "EvaluationLink", "to": "N27", "width": 1}, {"arrows": "to", "from": "L14", "title": "EvaluationLink", "to": "N15", "width": 1}, {"arrows": "to", "from": "L14", "title": "EvaluationLink", "to": "N23", "width": 1}, {"arrows": "to", "from": "L14", "title": "EvaluationLink", "to": "N27", "width": 1}, {"arrows": "to", "from": "L15", "title": "EvaluationLink", "to": "N15", "width": 1}, {"arrows": "to", "from": "L15", "title": "EvaluationLink", "to": "N24", "width": 1}, {"arrows": "to", "from": "L15", "title": "EvaluationLink", "to": "N27", "width": 1}, {"arrows": "to", "from": "L16", "title": "EvaluationLink", "to": "N20", "width": 1}, {"arrows": "to", "from": "L16", "title": "EvaluationLink", "to": "N22", "width": 1}, {"arrows": "to", "from": "L16", "title": "EvaluationLink", "to": "N28", "width": 1}, {"arrows": "to", "from": "L17", "title": "EvaluationLink", "to": "N21", "width": 1}, {"arrows": "to", "from": "L17", "title": "EvaluationLink", "to": "N22", "width": 1}, {"arrows": "to", "from": "L17", "title": "EvaluationLink", "to": "N28", "width": 1}, {"arrows": "to", "from": "L18", "title": "EvaluationLink", "to": "N125", "width": 1}, {"arrows": "to", "from": "L18", "title": "EvaluationLink", "to": "N25", "width": 1}, {"arrows": "to", "from": "L18", "title": "EvaluationLink", "to": "N26", "width": 1}, {"arrows": "to", "from": "L18", "title": "EvaluationLink", "to": "N30", "width": 1}, {"arrows": "to", "from": "L19", "title": "EvaluationLink", "to": "N15", "width": 1}, {"arrows": "to", "from": "L19", "title": "EvaluationLink", "to": "N9", "width": 1}, {"arrows": "to", "from": "L19", "title": "EvaluationLink", "to": "N10", "width": 1}, {"arrows": "to", "from": "L19", "title": "EvaluationLink", "to": "N31", "width": 1}, {"arrows": "to", "from": "L20", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L20", "title": "ContextLink", "to": "L13", "width": 1}, {"arrows": "to", "from": "L21", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L21", "title": "ContextLink", "to": "L14", "width": 1}, {"arrows": "to", "from": "L22", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L22", "title": "ContextLink", "to": "L15", "width": 1}, {"arrows": "to", "from": "L23", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L23", "title": "ContextLink", "to": "L16", "width": 1}, {"arrows": "to", "from": "L24", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L24", "title": "ContextLink", "to": "L17", "width": 1}, {"arrows": "to", "from": "L25", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L25", "title": "ContextLink", "to": "L18", "width": 1}, {"arrows": "to", "from": "L26", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L26", "title": "ContextLink", "to": "L19", "width": 1}, {"arrows": "to", "from": "L27", "title": "EvaluationLink", "to": "N35", "width": 1}, {"arrows": "to", "from": "L27", "title": "EvaluationLink", "to": "N36", "width": 1}, {"arrows": "to", "from": "L27", "title": "EvaluationLink", "to": "N49", "width": 1}, {"arrows": "to", "from": "L28", "title": "EvaluationLink", "to": "N44", "width": 1}, {"arrows": "to", "from": "L28", "title": "EvaluationLink", "to": "N42", "width": 1}, {"arrows": "to", "from": "L28", "title": "EvaluationLink", "to": "N48", "width": 1}, {"arrows": "to", "from": "L29", "title": "EvaluationLink", "to": "N43", "width": 1}, {"arrows": "to", "from": "L29", "title": "EvaluationLink", "to": "N39", "width": 1}, {"arrows": "to", "from": "L29", "title": "EvaluationLink", "to": "N40", "width": 1}, {"arrows": "to", "from": "L29", "title": "EvaluationLink", "to": "N45", "width": 1}, {"arrows": "to", "from": "L30", "title": "EvaluationLink", "to": "N43", "width": 1}, {"arrows": "to", "from": "L30", "title": "EvaluationLink", "to": "N42", "width": 1}, {"arrows": "to", "from": "L30", "title": "EvaluationLink", "to": "N41", "width": 1}, {"arrows": "to", "from": "L30", "title": "EvaluationLink", "to": "N47", "width": 1}, {"arrows": "to", "from": "L31", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L31", "title": "ContextLink", "to": "L27", "width": 1}, {"arrows": "to", "from": "L32", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L32", "title": "ContextLink", "to": "L28", "width": 1}, {"arrows": "to", "from": "L33", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L33", "title": "ContextLink", "to": "L29", "width": 1}, {"arrows": "to", "from": "L34", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L34", "title": "ContextLink", "to": "L30", "width": 1}, {"arrows": "to", "from": "L35", "title": "EvaluationLink", "to": "N51", "width": 1}, {"arrows": "to", "from": "L35", "title": "EvaluationLink", "to": "N80", "width": 1}, {"arrows": "to", "from": "L35", "title": "EvaluationLink", "to": "N60", "width": 1}, {"arrows": "to", "from": "L36", "title": "EvaluationLink", "to": "N51", "width": 1}, {"arrows": "to", "from": "L36", "title": "EvaluationLink", "to": "N97", "width": 1}, {"arrows": "to", "from": "L36", "title": "EvaluationLink", "to": "N60", "width": 1}, {"arrows": "to", "from": "L37", "title": "EvaluationLink", "to": "N51", "width": 1}, {"arrows": "to", "from": "L37", "title": "EvaluationLink", "to": "N20", "width": 1}, {"arrows": "to", "from": "L37", "title": "EvaluationLink", "to": "N60", "width": 1}, {"arrows": "to", "from": "L38", "title": "EvaluationLink", "to": "N80", "width": 1}, {"arrows": "to", "from": "L38", "title": "EvaluationLink", "to": "N97", "width": 1}, {"arrows": "to", "from": "L38", "title": "EvaluationLink", "to": "N54", "width": 1}, {"arrows": "to", "from": "L38", "title": "EvaluationLink", "to": "N62", "width": 1}, {"arrows": "to", "from": "L39", "title": "EvaluationLink", "to": "N52", "width": 1}, {"arrows": "to", "from": "L39", "title": "EvaluationLink", "to": "N80", "width": 1}, {"arrows": "to", "from": "L39", "title": "EvaluationLink", "to": "N97", "width": 1}, {"arrows": "to", "from": "L39", "title": "EvaluationLink", "to": "N61", "width": 1}, {"arrows": "to", "from": "L40", "title": "EvaluationLink", "to": "N52", "width": 1}, {"arrows": "to", "from": "L40", "title": "EvaluationLink", "to": "N80", "width": 1}, {"arrows": "to", "from": "L40", "title": "EvaluationLink", "to": "N97", "width": 1}, {"arrows": "to", "from": "L40", "title": "EvaluationLink", "to": "N60", "width": 1}, {"arrows": "to", "from": "L41", "title": "EvaluationLink", "to": "N59", "width": 1}, {"arrows": "to", "from": "L41", "title": "EvaluationLink", "to": "N61", "width": 1}, {"arrows": "to", "from": "L42", "title": "EvaluationLink", "to": "N58", "width": 1}, {"arrows": "to", "from": "L42", "title": "EvaluationLink", "to": "N64", "width": 1}, {"arrows": "to", "from": "L43", "title": "EvaluationLink", "to": "N80", "width": 1}, {"arrows": "to", "from": "L43", "title": "EvaluationLink", "to": "N177", "width": 1}, {"arrows": "to", "from": "L43", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L44", "title": "EvaluationLink", "to": "N97", "width": 1}, {"arrows": "to", "from": "L44", "title": "EvaluationLink", "to": "N177", "width": 1}, {"arrows": "to", "from": "L44", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L45", "title": "EvaluationLink", "to": "N20", "width": 1}, {"arrows": "to", "from": "L45", "title": "EvaluationLink", "to": "N177", "width": 1}, {"arrows": "to", "from": "L45", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L46", "title": "ContextLink", "to": "N50", "width": 1}, {"arrows": "to", "from": "L46", "title": "ContextLink", "to": "L35", "width": 1}, {"arrows": "to", "from": "L47", "title": "ContextLink", "to": "N50", "width": 1}, {"arrows": "to", "from": "L47", "title": "ContextLink", "to": "L36", "width": 1}, {"arrows": "to", "from": "L48", "title": "ContextLink", "to": "N50", "width": 1}, {"arrows": "to", "from": "L48", "title": "ContextLink", "to": "L37", "width": 1}, {"arrows": "to", "from": "L49", "title": "ContextLink", "to": "N50", "width": 1}, {"arrows": "to", "from": "L49", "title": "ContextLink", "to": "L38", "width": 1}, {"arrows": "to", "from": "L50", "title": "ContextLink", "to": "N50", "width": 1}, {"arrows": "to", "from": "L50", "title": "ContextLink", "to": "L39", "width": 1}, {"arrows": "to", "from": "L51", "title": "ContextLink", "to": "N50", "width": 1}, {"arrows": "to", "from": "L51", "title": "ContextLink", "to": "L40", "width": 1}, {"arrows": "to", "from": "L52", "title": "ContextLink", "to": "N50", "width": 1}, {"arrows": "to", "from": "L52", "title": "ContextLink", "to": "L41", "width": 1}, {"arrows": "to", "from": "L53", "title": "ContextLink", "to": "N50", "width": 1}, {"arrows": "to", "from": "L53", "title": "ContextLink", "to": "L42", "width": 1}, {"arrows": "to", "from": "L54", "title": "ContextLink", "to": "N50", "width": 1}, {"arrows": "to", "from": "L54", "title": "ContextLink", "to": "L43", "width": 1}, {"arrows": "to", "from": "L55", "title": "ContextLink", "to": "N50", "width": 1}, {"arrows": "to", "from": "L55", "title": "ContextLink", "to": "L44", "width": 1}, {"arrows": "to", "from": "L56", "title": "ContextLink", "to": "N50", "width": 1}, {"arrows": "to", "from": "L56", "title": "ContextLink", "to": "L45", "width": 1}, {"arrows": "to", "from": "L57", "title": "EvaluationLink", "to": "N67", "width": 1}, {"arrows": "to", "from": "L57", "title": "EvaluationLink", "to": "N69", "width": 1}, {"arrows": "to", "from": "L57", "title": "EvaluationLink", "to": "N46", "width": 1}, {"arrows": "to", "from": "L58", "title": "EvaluationLink", "to": "N67", "width": 1}, {"arrows": "to", "from": "L58", "title": "EvaluationLink", "to": "N71", "width": 1}, {"arrows": "to", "from": "L58", "title": "EvaluationLink", "to": "N70", "width": 1}, {"arrows": "to", "from": "L58", "title": "EvaluationLink", "to": "N72", "width": 1}, {"arrows": "to", "from": "L58", "title": "EvaluationLink", "to": "N87", "width": 1}, {"arrows": "to", "from": "L59", "title": "EvaluationLink", "to": "N73", "width": 1}, {"arrows": "to", "from": "L59", "title": "EvaluationLink", "to": "N74", "width": 1}, {"arrows": "to", "from": "L59", "title": "EvaluationLink", "to": "N91", "width": 1}, {"arrows": "to", "from": "L60", "title": "EvaluationLink", "to": "N67", "width": 1}, {"arrows": "to", "from": "L60", "title": "EvaluationLink", "to": "N75", "width": 1}, {"arrows": "to", "from": "L60", "title": "EvaluationLink", "to": "N76", "width": 1}, {"arrows": "to", "from": "L60", "title": "EvaluationLink", "to": "N88", "width": 1}, {"arrows": "to", "from": "L61", "title": "EvaluationLink", "to": "N67", "width": 1}, {"arrows": "to", "from": "L61", "title": "EvaluationLink", "to": "N75", "width": 1}, {"arrows": "to", "from": "L61", "title": "EvaluationLink", "to": "N77", "width": 1}, {"arrows": "to", "from": "L61", "title": "EvaluationLink", "to": "N88", "width": 1}, {"arrows": "to", "from": "L62", "title": "EvaluationLink", "to": "N67", "width": 1}, {"arrows": "to", "from": "L62", "title": "EvaluationLink", "to": "N75", "width": 1}, {"arrows": "to", "from": "L62", "title": "EvaluationLink", "to": "N78", "width": 1}, {"arrows": "to", "from": "L62", "title": "EvaluationLink", "to": "N88", "width": 1}, {"arrows": "to", "from": "L63", "title": "EvaluationLink", "to": "N79", "width": 1}, {"arrows": "to", "from": "L63", "title": "EvaluationLink", "to": "N92", "width": 1}, {"arrows": "to", "from": "L63", "title": "EvaluationLink", "to": "N80", "width": 1}, {"arrows": "to", "from": "L63", "title": "EvaluationLink", "to": "N89", "width": 1}, {"arrows": "to", "from": "L64", "title": "EvaluationLink", "to": "N79", "width": 1}, {"arrows": "to", "from": "L64", "title": "EvaluationLink", "to": "N82", "width": 1}, {"arrows": "to", "from": "L64", "title": "EvaluationLink", "to": "N89", "width": 1}, {"arrows": "to", "from": "L65", "title": "EvaluationLink", "to": "N83", "width": 1}, {"arrows": "to", "from": "L65", "title": "EvaluationLink", "to": "N79", "width": 1}, {"arrows": "to", "from": "L65", "title": "EvaluationLink", "to": "N90", "width": 1}, {"arrows": "to", "from": "L66", "title": "EvaluationLink", "to": "N84", "width": 1}, {"arrows": "to", "from": "L66", "title": "EvaluationLink", "to": "N79", "width": 1}, {"arrows": "to", "from": "L66", "title": "EvaluationLink", "to": "N90", "width": 1}, {"arrows": "to", "from": "L67", "title": "EvaluationLink", "to": "N85", "width": 1}, {"arrows": "to", "from": "L67", "title": "EvaluationLink", "to": "N79", "width": 1}, {"arrows": "to", "from": "L67", "title": "EvaluationLink", "to": "N90", "width": 1}, {"arrows": "to", "from": "L68", "title": "EvaluationLink", "to": "N86", "width": 1}, {"arrows": "to", "from": "L68", "title": "EvaluationLink", "to": "N79", "width": 1}, {"arrows": "to", "from": "L68", "title": "EvaluationLink", "to": "N90", "width": 1}, {"arrows": "to", "from": "L69", "title": "EvaluationLink", "to": "N79", "width": 1}, {"arrows": "to", "from": "L69", "title": "EvaluationLink", "to": "N80", "width": 1}, {"arrows": "to", "from": "L69", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L70", "title": "EvaluationLink", "to": "N79", "width": 1}, {"arrows": "to", "from": "L70", "title": "EvaluationLink", "to": "N73", "width": 1}, {"arrows": "to", "from": "L70", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L71", "title": "EvaluationLink", "to": "N79", "width": 1}, {"arrows": "to", "from": "L71", "title": "EvaluationLink", "to": "N74", "width": 1}, {"arrows": "to", "from": "L71", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L72", "title": "EvaluationLink", "to": "N79", "width": 1}, {"arrows": "to", "from": "L72", "title": "EvaluationLink", "to": "N81", "width": 1}, {"arrows": "to", "from": "L72", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L73", "title": "ContextLink", "to": "N66", "width": 1}, {"arrows": "to", "from": "L73", "title": "ContextLink", "to": "L57", "width": 1}, {"arrows": "to", "from": "L74", "title": "ContextLink", "to": "N66", "width": 1}, {"arrows": "to", "from": "L74", "title": "ContextLink", "to": "L58", "width": 1}, {"arrows": "to", "from": "L75", "title": "ContextLink", "to": "N66", "width": 1}, {"arrows": "to", "from": "L75", "title": "ContextLink", "to": "L59", "width": 1}, {"arrows": "to", "from": "L76", "title": "ContextLink", "to": "N66", "width": 1}, {"arrows": "to", "from": "L76", "title": "ContextLink", "to": "L60", "width": 1}, {"arrows": "to", "from": "L77", "title": "ContextLink", "to": "N66", "width": 1}, {"arrows": "to", "from": "L77", "title": "ContextLink", "to": "L61", "width": 1}, {"arrows": "to", "from": "L78", "title": "ContextLink", "to": "N66", "width": 1}, {"arrows": "to", "from": "L78", "title": "ContextLink", "to": "L62", "width": 1}, {"arrows": "to", "from": "L79", "title": "ContextLink", "to": "N66", "width": 1}, {"arrows": "to", "from": "L79", "title": "ContextLink", "to": "L63", "width": 1}, {"arrows": "to", "from": "L80", "title": "ContextLink", "to": "N66", "width": 1}, {"arrows": "to", "from": "L80", "title": "ContextLink", "to": "L64", "width": 1}, {"arrows": "to", "from": "L81", "title": "ContextLink", "to": "N66", "width": 1}, {"arrows": "to", "from": "L81", "title": "ContextLink", "to": "L65", "width": 1}, {"arrows": "to", "from": "L82", "title": "ContextLink", "to": "N66", "width": 1}, {"arrows": "to", "from": "L82", "title": "ContextLink", "to": "L66", "width": 1}, {"arrows": "to", "from": "L83", "title": "ContextLink", "to": "N66", "width": 1}, {"arrows": "to", "from": "L83", "title": "ContextLink", "to": "L67", "width": 1}, {"arrows": "to", "from": "L84", "title": "ContextLink", "to": "N66", "width": 1}, {"arrows": "to", "from": "L84", "title": "ContextLink", "to": "L68", "width": 1}, {"arrows": "to", "from": "L85", "title": "ContextLink", "to": "N66", "width": 1}, {"arrows": "to", "from": "L85", "title": "ContextLink", "to": "L69", "width": 1}, {"arrows": "to", "from": "L86", "title": "ContextLink", "to": "N66", "width": 1}, {"arrows": "to", "from": "L86", "title": "ContextLink", "to": "L70", "width": 1}, {"arrows": "to", "from": "L87", "title": "ContextLink", "to": "N66", "width": 1}, {"arrows": "to", "from": "L87", "title": "ContextLink", "to": "L71", "width": 1}, {"arrows": "to", "from": "L88", "title": "ContextLink", "to": "N66", "width": 1}, {"arrows": "to", "from": "L88", "title": "ContextLink", "to": "L72", "width": 1}, {"arrows": "to", "from": "L89", "title": "EvaluationLink", "to": "N115", "width": 1}, {"arrows": "to", "from": "L89", "title": "EvaluationLink", "to": "N94", "width": 1}, {"arrows": "to", "from": "L90", "title": "EvaluationLink", "to": "N115", "width": 1}, {"arrows": "to", "from": "L90", "title": "EvaluationLink", "to": "N95", "width": 1}, {"arrows": "to", "from": "L90", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L91", "title": "EvaluationLink", "to": "N80", "width": 1}, {"arrows": "to", "from": "L91", "title": "EvaluationLink", "to": "N97", "width": 1}, {"arrows": "to", "from": "L91", "title": "EvaluationLink", "to": "N115", "width": 1}, {"arrows": "to", "from": "L91", "title": "EvaluationLink", "to": "N106", "width": 1}, {"arrows": "to", "from": "L92", "title": "EvaluationLink", "to": "N98", "width": 1}, {"arrows": "to", "from": "L92", "title": "EvaluationLink", "to": "N115", "width": 1}, {"arrows": "to", "from": "L92", "title": "EvaluationLink", "to": "N107", "width": 1}, {"arrows": "to", "from": "L93", "title": "EvaluationLink", "to": "N98", "width": 1}, {"arrows": "to", "from": "L93", "title": "EvaluationLink", "to": "N115", "width": 1}, {"arrows": "to", "from": "L93", "title": "EvaluationLink", "to": "N108", "width": 1}, {"arrows": "to", "from": "L94", "title": "EvaluationLink", "to": "N104", "width": 1}, {"arrows": "to", "from": "L94", "title": "EvaluationLink", "to": "N105", "width": 1}, {"arrows": "to", "from": "L94", "title": "EvaluationLink", "to": "N42", "width": 1}, {"arrows": "to", "from": "L94", "title": "EvaluationLink", "to": "N99", "width": 1}, {"arrows": "to", "from": "L94", "title": "EvaluationLink", "to": "N100", "width": 1}, {"arrows": "to", "from": "L94", "title": "EvaluationLink", "to": "N109", "width": 1}, {"arrows": "to", "from": "L95", "title": "EvaluationLink", "to": "N42", "width": 1}, {"arrows": "to", "from": "L95", "title": "EvaluationLink", "to": "N99", "width": 1}, {"arrows": "to", "from": "L95", "title": "EvaluationLink", "to": "N100", "width": 1}, {"arrows": "to", "from": "L95", "title": "EvaluationLink", "to": "N110", "width": 1}, {"arrows": "to", "from": "L96", "title": "EvaluationLink", "to": "N99", "width": 1}, {"arrows": "to", "from": "L96", "title": "EvaluationLink", "to": "N100", "width": 1}, {"arrows": "to", "from": "L96", "title": "EvaluationLink", "to": "N111", "width": 1}, {"arrows": "to", "from": "L97", "title": "EvaluationLink", "to": "N42", "width": 1}, {"arrows": "to", "from": "L97", "title": "EvaluationLink", "to": "N102", "width": 1}, {"arrows": "to", "from": "L97", "title": "EvaluationLink", "to": "N103", "width": 1}, {"arrows": "to", "from": "L97", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L98", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L98", "title": "ContextLink", "to": "L89", "width": 1}, {"arrows": "to", "from": "L99", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L99", "title": "ContextLink", "to": "L90", "width": 1}, {"arrows": "to", "from": "L100", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L100", "title": "ContextLink", "to": "L91", "width": 1}, {"arrows": "to", "from": "L101", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L101", "title": "ContextLink", "to": "L92", "width": 1}, {"arrows": "to", "from": "L102", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L102", "title": "ContextLink", "to": "L93", "width": 1}, {"arrows": "to", "from": "L103", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L103", "title": "ContextLink", "to": "L94", "width": 1}, {"arrows": "to", "from": "L104", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L104", "title": "ContextLink", "to": "L95", "width": 1}, {"arrows": "to", "from": "L105", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L105", "title": "ContextLink", "to": "L96", "width": 1}, {"arrows": "to", "from": "L106", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L106", "title": "ContextLink", "to": "L97", "width": 1}, {"arrows": "to", "from": "L107", "title": "EvaluationLink", "to": "N118", "width": 1}, {"arrows": "to", "from": "L107", "title": "EvaluationLink", "to": "N74", "width": 1}, {"arrows": "to", "from": "L107", "title": "EvaluationLink", "to": "N80", "width": 1}, {"arrows": "to", "from": "L107", "title": "EvaluationLink", "to": "N117", "width": 1}, {"arrows": "to", "from": "L107", "title": "EvaluationLink", "to": "N114", "width": 1}, {"arrows": "to", "from": "L107", "title": "EvaluationLink", "to": "N115", "width": 1}, {"arrows": "to", "from": "L107", "title": "EvaluationLink", "to": "N116", "width": 1}, {"arrows": "to", "from": "L108", "title": "EvaluationLink", "to": "N119", "width": 1}, {"arrows": "to", "from": "L108", "title": "EvaluationLink", "to": "N121", "width": 1}, {"arrows": "to", "from": "L108", "title": "EvaluationLink", "to": "N115", "width": 1}, {"arrows": "to", "from": "L108", "title": "EvaluationLink", "to": "N120", "width": 1}, {"arrows": "to", "from": "L109", "title": "EvaluationLink", "to": "N119", "width": 1}, {"arrows": "to", "from": "L109", "title": "EvaluationLink", "to": "N116", "width": 1}, {"arrows": "to", "from": "L109", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L110", "title": "EvaluationLink", "to": "N125", "width": 1}, {"arrows": "to", "from": "L110", "title": "EvaluationLink", "to": "N42", "width": 1}, {"arrows": "to", "from": "L110", "title": "EvaluationLink", "to": "N126", "width": 1}, {"arrows": "to", "from": "L110", "title": "EvaluationLink", "to": "N128", "width": 1}, {"arrows": "to", "from": "L111", "title": "ContextLink", "to": "N113", "width": 1}, {"arrows": "to", "from": "L111", "title": "ContextLink", "to": "L107", "width": 1}, {"arrows": "to", "from": "L112", "title": "ContextLink", "to": "N129", "width": 1}, {"arrows": "to", "from": "L112", "title": "ContextLink", "to": "N113", "width": 1}, {"arrows": "to", "from": "L112", "title": "ContextLink", "to": "L108", "width": 1}, {"arrows": "to", "from": "L113", "title": "ContextLink", "to": "N113", "width": 1}, {"arrows": "to", "from": "L113", "title": "ContextLink", "to": "L109", "width": 1}, {"arrows": "to", "from": "L114", "title": "ContextLink", "to": "N113", "width": 1}, {"arrows": "to", "from": "L114", "title": "ContextLink", "to": "L110", "width": 1}, {"arrows": "to", "from": "L115", "title": "EvaluationLink", "to": "N81", "width": 1}, {"arrows": "to", "from": "L115", "title": "EvaluationLink", "to": "N80", "width": 1}, {"arrows": "to", "from": "L115", "title": "EvaluationLink", "to": "N134", "width": 1}, {"arrows": "to", "from": "L115", "title": "EvaluationLink", "to": "N135", "width": 1}, {"arrows": "to", "from": "L115", "title": "EvaluationLink", "to": "N136", "width": 1}, {"arrows": "to", "from": "L116", "title": "EvaluationLink", "to": "L115", "width": 1}, {"arrows": "to", "from": "L116", "title": "EvaluationLink", "to": "N103", "width": 1}, {"arrows": "to", "from": "L116", "title": "EvaluationLink", "to": "N137", "width": 1}, {"arrows": "to", "from": "L117", "title": "EvaluationLink", "to": "N131", "width": 1}, {"arrows": "to", "from": "L117", "title": "EvaluationLink", "to": "N126", "width": 1}, {"arrows": "to", "from": "L117", "title": "EvaluationLink", "to": "N141", "width": 1}, {"arrows": "to", "from": "L118", "title": "EvaluationLink", "to": "N131", "width": 1}, {"arrows": "to", "from": "L118", "title": "EvaluationLink", "to": "N126", "width": 1}, {"arrows": "to", "from": "L118", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L119", "title": "EvaluationLink", "to": "N74", "width": 1}, {"arrows": "to", "from": "L119", "title": "EvaluationLink", "to": "N132", "width": 1}, {"arrows": "to", "from": "L119", "title": "EvaluationLink", "to": "N138", "width": 1}, {"arrows": "to", "from": "L120", "title": "EvaluationLink", "to": "N132", "width": 1}, {"arrows": "to", "from": "L120", "title": "EvaluationLink", "to": "N126", "width": 1}, {"arrows": "to", "from": "L120", "title": "EvaluationLink", "to": "N139", "width": 1}, {"arrows": "to", "from": "L121", "title": "EvaluationLink", "to": "N132", "width": 1}, {"arrows": "to", "from": "L121", "title": "EvaluationLink", "to": "N133", "width": 1}, {"arrows": "to", "from": "L121", "title": "EvaluationLink", "to": "N140", "width": 1}, {"arrows": "to", "from": "L122", "title": "EvaluationLink", "to": "N67", "width": 1}, {"arrows": "to", "from": "L122", "title": "EvaluationLink", "to": "N133", "width": 1}, {"arrows": "to", "from": "L122", "title": "EvaluationLink", "to": "N140", "width": 1}, {"arrows": "to", "from": "L123", "title": "ContextLink", "to": "N130", "width": 1}, {"arrows": "to", "from": "L123", "title": "ContextLink", "to": "L115", "width": 1}, {"arrows": "to", "from": "L124", "title": "ContextLink", "to": "N130", "width": 1}, {"arrows": "to", "from": "L124", "title": "ContextLink", "to": "L116", "width": 1}, {"arrows": "to", "from": "L125", "title": "ContextLink", "to": "N130", "width": 1}, {"arrows": "to", "from": "L125", "title": "ContextLink", "to": "L117", "width": 1}, {"arrows": "to", "from": "L126", "title": "ContextLink", "to": "N130", "width": 1}, {"arrows": "to", "from": "L126", "title": "ContextLink", "to": "L118", "width": 1}, {"arrows": "to", "from": "L127", "title": "ContextLink", "to": "N130", "width": 1}, {"arrows": "to", "from": "L127", "title": "ContextLink", "to": "L119", "width": 1}, {"arrows": "to", "from": "L128", "title": "ContextLink", "to": "N130", "width": 1}, {"arrows": "to", "from": "L128", "title": "ContextLink", "to": "L120", "width": 1}, {"arrows": "to", "from": "L129", "title": "ContextLink", "to": "N130", "width": 1}, {"arrows": "to", "from": "L129", "title": "ContextLink", "to": "L121", "width": 1}, {"arrows": "to", "from": "L130", "title": "ContextLink", "to": "N130", "width": 1}, {"arrows": "to", "from": "L130", "title": "ContextLink", "to": "L122", "width": 1}, {"arrows": "to", "from": "L131", "title": "EvaluationLink", "to": "N67", "width": 1}, {"arrows": "to", "from": "L131", "title": "EvaluationLink", "to": "N143", "width": 1}, {"arrows": "to", "from": "L131", "title": "EvaluationLink", "to": "N144", "width": 1}, {"arrows": "to", "from": "L131", "title": "EvaluationLink", "to": "N88", "width": 1}, {"arrows": "to", "from": "L132", "title": "EvaluationLink", "to": "N145", "width": 1}, {"arrows": "to", "from": "L132", "title": "EvaluationLink", "to": "N34", "width": 1}, {"arrows": "to", "from": "L132", "title": "EvaluationLink", "to": "N60", "width": 1}, {"arrows": "to", "from": "L133", "title": "EvaluationLink", "to": "N145", "width": 1}, {"arrows": "to", "from": "L133", "title": "EvaluationLink", "to": "N97", "width": 1}, {"arrows": "to", "from": "L133", "title": "EvaluationLink", "to": "N60", "width": 1}, {"arrows": "to", "from": "L134", "title": "EvaluationLink", "to": "N146", "width": 1}, {"arrows": "to", "from": "L134", "title": "EvaluationLink", "to": "N147", "width": 1}, {"arrows": "to", "from": "L134", "title": "EvaluationLink", "to": "N148", "width": 1}, {"arrows": "to", "from": "L134", "title": "EvaluationLink", "to": "N145", "width": 1}, {"arrows": "to", "from": "L134", "title": "EvaluationLink", "to": "N14", "width": 1}, {"arrows": "to", "from": "L134", "title": "EvaluationLink", "to": "N87", "width": 1}, {"arrows": "to", "from": "L135", "title": "EvaluationLink", "to": "N145", "width": 1}, {"arrows": "to", "from": "L135", "title": "EvaluationLink", "to": "N149", "width": 1}, {"arrows": "to", "from": "L135", "title": "EvaluationLink", "to": "N90", "width": 1}, {"arrows": "to", "from": "L136", "title": "EvaluationLink", "to": "N150", "width": 1}, {"arrows": "to", "from": "L136", "title": "EvaluationLink", "to": "N149", "width": 1}, {"arrows": "to", "from": "L136", "title": "EvaluationLink", "to": "N90", "width": 1}, {"arrows": "to", "from": "L137", "title": "EvaluationLink", "to": "N151", "width": 1}, {"arrows": "to", "from": "L137", "title": "EvaluationLink", "to": "N149", "width": 1}, {"arrows": "to", "from": "L137", "title": "EvaluationLink", "to": "N90", "width": 1}, {"arrows": "to", "from": "L138", "title": "EvaluationLink", "to": "N152", "width": 1}, {"arrows": "to", "from": "L138", "title": "EvaluationLink", "to": "N149", "width": 1}, {"arrows": "to", "from": "L138", "title": "EvaluationLink", "to": "N90", "width": 1}, {"arrows": "to", "from": "L139", "title": "EvaluationLink", "to": "N149", "width": 1}, {"arrows": "to", "from": "L139", "title": "EvaluationLink", "to": "N153", "width": 1}, {"arrows": "to", "from": "L139", "title": "EvaluationLink", "to": "N154", "width": 1}, {"arrows": "to", "from": "L140", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L140", "title": "ContextLink", "to": "L131", "width": 1}, {"arrows": "to", "from": "L141", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L141", "title": "ContextLink", "to": "L132", "width": 1}, {"arrows": "to", "from": "L142", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L142", "title": "ContextLink", "to": "L133", "width": 1}, {"arrows": "to", "from": "L143", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L143", "title": "ContextLink", "to": "L134", "width": 1}, {"arrows": "to", "from": "L144", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L144", "title": "ContextLink", "to": "L135", "width": 1}, {"arrows": "to", "from": "L145", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L145", "title": "ContextLink", "to": "L136", "width": 1}, {"arrows": "to", "from": "L146", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L146", "title": "ContextLink", "to": "L137", "width": 1}, {"arrows": "to", "from": "L147", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L147", "title": "ContextLink", "to": "L138", "width": 1}, {"arrows": "to", "from": "L148", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L148", "title": "ContextLink", "to": "L139", "width": 1}, {"arrows": "to", "from": "L149", "title": "EvaluationLink", "to": "N97", "width": 1}, {"arrows": "to", "from": "L149", "title": "EvaluationLink", "to": "N157", "width": 1}, {"arrows": "to", "from": "L149", "title": "EvaluationLink", "to": "N169", "width": 1}, {"arrows": "to", "from": "L150", "title": "EvaluationLink", "to": "N167", "width": 1}, {"arrows": "to", "from": "L150", "title": "EvaluationLink", "to": "N158", "width": 1}, {"arrows": "to", "from": "L150", "title": "EvaluationLink", "to": "N168", "width": 1}, {"arrows": "to", "from": "L151", "title": "EvaluationLink", "to": "N167", "width": 1}, {"arrows": "to", "from": "L151", "title": "EvaluationLink", "to": "N159", "width": 1}, {"arrows": "to", "from": "L151", "title": "EvaluationLink", "to": "N168", "width": 1}, {"arrows": "to", "from": "L152", "title": "EvaluationLink", "to": "N167", "width": 1}, {"arrows": "to", "from": "L152", "title": "EvaluationLink", "to": "N160", "width": 1}, {"arrows": "to", "from": "L152", "title": "EvaluationLink", "to": "N161", "width": 1}, {"arrows": "to", "from": "L152", "title": "EvaluationLink", "to": "N168", "width": 1}, {"arrows": "to", "from": "L153", "title": "EvaluationLink", "to": "N167", "width": 1}, {"arrows": "to", "from": "L153", "title": "EvaluationLink", "to": "N162", "width": 1}, {"arrows": "to", "from": "L153", "title": "EvaluationLink", "to": "N163", "width": 1}, {"arrows": "to", "from": "L153", "title": "EvaluationLink", "to": "N168", "width": 1}, {"arrows": "to", "from": "L154", "title": "EvaluationLink", "to": "N167", "width": 1}, {"arrows": "to", "from": "L154", "title": "EvaluationLink", "to": "N162", "width": 1}, {"arrows": "to", "from": "L154", "title": "EvaluationLink", "to": "N164", "width": 1}, {"arrows": "to", "from": "L154", "title": "EvaluationLink", "to": "N168", "width": 1}, {"arrows": "to", "from": "L155", "title": "EvaluationLink", "to": "N165", "width": 1}, {"arrows": "to", "from": "L155", "title": "EvaluationLink", "to": "N167", "width": 1}, {"arrows": "to", "from": "L155", "title": "EvaluationLink", "to": "N60", "width": 1}, {"arrows": "to", "from": "L156", "title": "EvaluationLink", "to": "N167", "width": 1}, {"arrows": "to", "from": "L156", "title": "EvaluationLink", "to": "N166", "width": 1}, {"arrows": "to", "from": "L156", "title": "EvaluationLink", "to": "N168", "width": 1}, {"arrows": "to", "from": "L157", "title": "EvaluationLink", "to": "N165", "width": 1}, {"arrows": "to", "from": "L157", "title": "EvaluationLink", "to": "N177", "width": 1}, {"arrows": "to", "from": "L157", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L158", "title": "EvaluationLink", "to": "N167", "width": 1}, {"arrows": "to", "from": "L158", "title": "EvaluationLink", "to": "N177", "width": 1}, {"arrows": "to", "from": "L158", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L159", "title": "ContextLink", "to": "N170", "width": 1}, {"arrows": "to", "from": "L159", "title": "ContextLink", "to": "L149", "width": 1}, {"arrows": "to", "from": "L160", "title": "ContextLink", "to": "N170", "width": 1}, {"arrows": "to", "from": "L160", "title": "ContextLink", "to": "L150", "width": 1}, {"arrows": "to", "from": "L161", "title": "ContextLink", "to": "N170", "width": 1}, {"arrows": "to", "from": "L161", "title": "ContextLink", "to": "L151", "width": 1}, {"arrows": "to", "from": "L162", "title": "ContextLink", "to": "N170", "width": 1}, {"arrows": "to", "from": "L162", "title": "ContextLink", "to": "L152", "width": 1}, {"arrows": "to", "from": "L163", "title": "ContextLink", "to": "N170", "width": 1}, {"arrows": "to", "from": "L163", "title": "ContextLink", "to": "L153", "width": 1}, {"arrows": "to", "from": "L164", "title": "ContextLink", "to": "N170", "width": 1}, {"arrows": "to", "from": "L164", "title": "ContextLink", "to": "L154", "width": 1}, {"arrows": "to", "from": "L165", "title": "ContextLink", "to": "N170", "width": 1}, {"arrows": "to", "from": "L165", "title": "ContextLink", "to": "L155", "width": 1}, {"arrows": "to", "from": "L166", "title": "ContextLink", "to": "N170", "width": 1}, {"arrows": "to", "from": "L166", "title": "ContextLink", "to": "L156", "width": 1}, {"arrows": "to", "from": "L167", "title": "ContextLink", "to": "N170", "width": 1}, {"arrows": "to", "from": "L167", "title": "ContextLink", "to": "L157", "width": 1}, {"arrows": "to", "from": "L168", "title": "ContextLink", "to": "N170", "width": 1}, {"arrows": "to", "from": "L168", "title": "ContextLink", "to": "L158", "width": 1}, {"arrows": "to", "from": "L169", "title": "EvaluationLink", "to": "N67", "width": 1}, {"arrows": "to", "from": "L169", "title": "EvaluationLink", "to": "N153", "width": 1}, {"arrows": "to", "from": "L169", "title": "EvaluationLink", "to": "N180", "width": 1}, {"arrows": "to", "from": "L169", "title": "EvaluationLink", "to": "N45", "width": 1}, {"arrows": "to", "from": "L170", "title": "EvaluationLink", "to": "N67", "width": 1}, {"arrows": "to", "from": "L170", "title": "EvaluationLink", "to": "N193", "width": 1}, {"arrows": "to", "from": "L170", "title": "EvaluationLink", "to": "N180", "width": 1}, {"arrows": "to", "from": "L170", "title": "EvaluationLink", "to": "N45", "width": 1}, {"arrows": "to", "from": "L171", "title": "EvaluationLink", "to": "N74", "width": 1}, {"arrows": "to", "from": "L171", "title": "EvaluationLink", "to": "N174", "width": 1}, {"arrows": "to", "from": "L171", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L172", "title": "EvaluationLink", "to": "N73", "width": 1}, {"arrows": "to", "from": "L172", "title": "EvaluationLink", "to": "N174", "width": 1}, {"arrows": "to", "from": "L172", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L173", "title": "EvaluationLink", "to": "N174", "width": 1}, {"arrows": "to", "from": "L173", "title": "EvaluationLink", "to": "N181", "width": 1}, {"arrows": "to", "from": "L173", "title": "EvaluationLink", "to": "N176", "width": 1}, {"arrows": "to", "from": "L173", "title": "EvaluationLink", "to": "N73", "width": 1}, {"arrows": "to", "from": "L173", "title": "EvaluationLink", "to": "N90", "width": 1}, {"arrows": "to", "from": "L174", "title": "EvaluationLink", "to": "N174", "width": 1}, {"arrows": "to", "from": "L174", "title": "EvaluationLink", "to": "N181", "width": 1}, {"arrows": "to", "from": "L174", "title": "EvaluationLink", "to": "N67", "width": 1}, {"arrows": "to", "from": "L174", "title": "EvaluationLink", "to": "N90", "width": 1}, {"arrows": "to", "from": "L175", "title": "EvaluationLink", "to": "N181", "width": 1}, {"arrows": "to", "from": "L175", "title": "EvaluationLink", "to": "N67", "width": 1}, {"arrows": "to", "from": "L175", "title": "EvaluationLink", "to": "N177", "width": 1}, {"arrows": "to", "from": "L175", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L176", "title": "EvaluationLink", "to": "N178", "width": 1}, {"arrows": "to", "from": "L176", "title": "EvaluationLink", "to": "N67", "width": 1}, {"arrows": "to", "from": "L176", "title": "EvaluationLink", "to": "N60", "width": 1}, {"arrows": "to", "from": "L177", "title": "ContextLink", "to": "N170", "width": 1}, {"arrows": "to", "from": "L177", "title": "ContextLink", "to": "L169", "width": 1}, {"arrows": "to", "from": "L178", "title": "ContextLink", "to": "N170", "width": 1}, {"arrows": "to", "from": "L178", "title": "ContextLink", "to": "L170", "width": 1}, {"arrows": "to", "from": "L179", "title": "ContextLink", "to": "N170", "width": 1}, {"arrows": "to", "from": "L179", "title": "ContextLink", "to": "L171", "width": 1}, {"arrows": "to", "from": "L180", "title": "ContextLink", "to": "N170", "width": 1}, {"arrows": "to", "from": "L180", "title": "ContextLink", "to": "L172", "width": 1}, {"arrows": "to", "from": "L181", "title": "ContextLink", "to": "N170", "width": 1}, {"arrows": "to", "from": "L181", "title": "ContextLink", "to": "L173", "width": 1}, {"arrows": "to", "from": "L182", "title": "ContextLink", "to": "N170", "width": 1}, {"arrows": "to", "from": "L182", "title": "ContextLink", "to": "L174", "width": 1}, {"arrows": "to", "from": "L183", "title": "ContextLink", "to": "N170", "width": 1}, {"arrows": "to", "from": "L183", "title": "ContextLink", "to": "L175", "width": 1}, {"arrows": "to", "from": "L184", "title": "ContextLink", "to": "N170", "width": 1}, {"arrows": "to", "from": "L184", "title": "ContextLink", "to": "L176", "width": 1}, {"arrows": "to", "from": "L185", "title": "EvaluationLink", "to": "N67", "width": 1}, {"arrows": "to", "from": "L185", "title": "EvaluationLink", "to": "N143", "width": 1}, {"arrows": "to", "from": "L185", "title": "EvaluationLink", "to": "N153", "width": 1}, {"arrows": "to", "from": "L185", "title": "EvaluationLink", "to": "N187", "width": 1}, {"arrows": "to", "from": "L186", "title": "EvaluationLink", "to": "N67", "width": 1}, {"arrows": "to", "from": "L186", "title": "EvaluationLink", "to": "N184", "width": 1}, {"arrows": "to", "from": "L186", "title": "EvaluationLink", "to": "N153", "width": 1}, {"arrows": "to", "from": "L186", "title": "EvaluationLink", "to": "N187", "width": 1}, {"arrows": "to", "from": "L187", "title": "EvaluationLink", "to": "N67", "width": 1}, {"arrows": "to", "from": "L187", "title": "EvaluationLink", "to": "N178", "width": 1}, {"arrows": "to", "from": "L187", "title": "EvaluationLink", "to": "N193", "width": 1}, {"arrows": "to", "from": "L187", "title": "EvaluationLink", "to": "N187", "width": 1}, {"arrows": "to", "from": "L188", "title": "EvaluationLink", "to": "N67", "width": 1}, {"arrows": "to", "from": "L188", "title": "EvaluationLink", "to": "N182", "width": 1}, {"arrows": "to", "from": "L188", "title": "EvaluationLink", "to": "N40", "width": 1}, {"arrows": "to", "from": "L188", "title": "EvaluationLink", "to": "N180", "width": 1}, {"arrows": "to", "from": "L188", "title": "EvaluationLink", "to": "N188", "width": 1}, {"arrows": "to", "from": "L189", "title": "EvaluationLink", "to": "N67", "width": 1}, {"arrows": "to", "from": "L189", "title": "EvaluationLink", "to": "N183", "width": 1}, {"arrows": "to", "from": "L189", "title": "EvaluationLink", "to": "N188", "width": 1}, {"arrows": "to", "from": "L190", "title": "EvaluationLink", "to": "N181", "width": 1}, {"arrows": "to", "from": "L190", "title": "EvaluationLink", "to": "N67", "width": 1}, {"arrows": "to", "from": "L190", "title": "EvaluationLink", "to": "N143", "width": 1}, {"arrows": "to", "from": "L190", "title": "EvaluationLink", "to": "N187", "width": 1}, {"arrows": "to", "from": "L191", "title": "EvaluationLink", "to": "N181", "width": 1}, {"arrows": "to", "from": "L191", "title": "EvaluationLink", "to": "N153", "width": 1}, {"arrows": "to", "from": "L191", "title": "EvaluationLink", "to": "N189", "width": 1}, {"arrows": "to", "from": "L192", "title": "EvaluationLink", "to": "N67", "width": 1}, {"arrows": "to", "from": "L192", "title": "EvaluationLink", "to": "N184", "width": 1}, {"arrows": "to", "from": "L192", "title": "EvaluationLink", "to": "N182", "width": 1}, {"arrows": "to", "from": "L192", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L193", "title": "ContextLink", "to": "N179", "width": 1}, {"arrows": "to", "from": "L193", "title": "ContextLink", "to": "L185", "width": 1}, {"arrows": "to", "from": "L194", "title": "ContextLink", "to": "N179", "width": 1}, {"arrows": "to", "from": "L194", "title": "ContextLink", "to": "L186", "width": 1}, {"arrows": "to", "from": "L195", "title": "ContextLink", "to": "N179", "width": 1}, {"arrows": "to", "from": "L195", "title": "ContextLink", "to": "L187", "width": 1}, {"arrows": "to", "from": "L196", "title": "ContextLink", "to": "N179", "width": 1}, {"arrows": "to", "from": "L196", "title": "ContextLink", "to": "L188", "width": 1}, {"arrows": "to", "from": "L197", "title": "ContextLink", "to": "N179", "width": 1}, {"arrows": "to", "from": "L197", "title": "ContextLink", "to": "L189", "width": 1}, {"arrows": "to", "from": "L198", "title": "ContextLink", "to": "N179", "width": 1}, {"arrows": "to", "from": "L198", "title": "ContextLink", "to": "L190", "width": 1}, {"arrows": "to", "from": "L199", "title": "ContextLink", "to": "N179", "width": 1}, {"arrows": "to", "from": "L199", "title": "ContextLink", "to": "L191", "width": 1}, {"arrows": "to", "from": "L200", "title": "ContextLink", "to": "N179", "width": 1}, {"arrows": "to", "from": "L200", "title": "ContextLink", "to": "L192", "width": 1}, {"arrows": "to", "from": "L201", "title": "EvaluationLink", "to": "N67", "width": 1}, {"arrows": "to", "from": "L201", "title": "EvaluationLink", "to": "N103", "width": 1}, {"arrows": "to", "from": "L201", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L202", "title": "EvaluationLink", "to": "N58", "width": 1}, {"arrows": "to", "from": "L202", "title": "EvaluationLink", "to": "N196", "width": 1}, {"arrows": "to", "from": "L202", "title": "EvaluationLink", "to": "N197", "width": 1}, {"arrows": "to", "from": "L202", "title": "EvaluationLink", "to": "N195", "width": 1}, {"arrows": "to", "from": "L203", "title": "EvaluationLink", "to": "N73", "width": 1}, {"arrows": "to", "from": "L203", "title": "EvaluationLink", "to": "N197", "width": 1}, {"arrows": "to", "from": "L203", "title": "EvaluationLink", "to": "N92", "width": 1}, {"arrows": "to", "from": "L204", "title": "ContextLink", "to": "N190", "width": 1}, {"arrows": "to", "from": "L204", "title": "ContextLink", "to": "L201", "width": 1}, {"arrows": "to", "from": "L205", "title": "ContextLink", "to": "N190", "width": 1}, {"arrows": "to", "from": "L205", "title": "ContextLink", "to": "L202", "width": 1}, {"arrows": "to", "from": "L206", "title": "ContextLink", "to": "N190", "width": 1}, {"arrows": "to", "from": "L206", "title": "ContextLink", "to": "L203", "width": 1}, {"arrows": "to", "from": "L207", "title": "EvaluationLink", "to": "N193", "width": 1}, {"arrows": "to", "from": "L207", "title": "EvaluationLink", "to": "N144", "width": 1}, {"arrows": "to", "from": "L207", "title": "EvaluationLink", "to": "N208", "width": 1}, {"arrows": "to", "from": "L208", "title": "EvaluationLink", "to": "N200", "width": 1}, {"arrows": "to", "from": "L208", "title": "EvaluationLink", "to": "N191", "width": 1}, {"arrows": "to", "from": "L208", "title": "EvaluationLink", "to": "N192", "width": 1}, {"arrows": "to", "from": "L208", "title": "EvaluationLink", "to": "N42", "width": 1}, {"arrows": "to", "from": "L208", "title": "EvaluationLink", "to": "N209", "width": 1}, {"arrows": "to", "from": "L209", "title": "EvaluationLink", "to": "N42", "width": 1}, {"arrows": "to", "from": "L209", "title": "EvaluationLink", "to": "N199", "width": 1}, {"arrows": "to", "from": "L209", "title": "EvaluationLink", "to": "N103", "width": 1}, {"arrows": "to", "from": "L209", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L210", "title": "EvaluationLink", "to": "N42", "width": 1}, {"arrows": "to", "from": "L210", "title": "EvaluationLink", "to": "N144", "width": 1}, {"arrows": "to", "from": "L210", "title": "EvaluationLink", "to": "N103", "width": 1}, {"arrows": "to", "from": "L210", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L211", "title": "EvaluationLink", "to": "N200", "width": 1}, {"arrows": "to", "from": "L211", "title": "EvaluationLink", "to": "N206", "width": 1}, {"arrows": "to", "from": "L211", "title": "EvaluationLink", "to": "N201", "width": 1}, {"arrows": "to", "from": "L211", "title": "EvaluationLink", "to": "N213", "width": 1}, {"arrows": "to", "from": "L212", "title": "EvaluationLink", "to": "N200", "width": 1}, {"arrows": "to", "from": "L212", "title": "EvaluationLink", "to": "N207", "width": 1}, {"arrows": "to", "from": "L212", "title": "EvaluationLink", "to": "N201", "width": 1}, {"arrows": "to", "from": "L212", "title": "EvaluationLink", "to": "N212", "width": 1}, {"arrows": "to", "from": "L213", "title": "EvaluationLink", "to": "N200", "width": 1}, {"arrows": "to", "from": "L213", "title": "EvaluationLink", "to": "N205", "width": 1}, {"arrows": "to", "from": "L213", "title": "EvaluationLink", "to": "N214", "width": 1}, {"arrows": "to", "from": "L214", "title": "EvaluationLink", "to": "N201", "width": 1}, {"arrows": "to", "from": "L214", "title": "EvaluationLink", "to": "N202", "width": 1}, {"arrows": "to", "from": "L214", "title": "EvaluationLink", "to": "N212", "width": 1}, {"arrows": "to", "from": "L215", "title": "EvaluationLink", "to": "N201", "width": 1}, {"arrows": "to", "from": "L215", "title": "EvaluationLink", "to": "N203", "width": 1}, {"arrows": "to", "from": "L215", "title": "EvaluationLink", "to": "N212", "width": 1}, {"arrows": "to", "from": "L216", "title": "EvaluationLink", "to": "N201", "width": 1}, {"arrows": "to", "from": "L216", "title": "EvaluationLink", "to": "N204", "width": 1}, {"arrows": "to", "from": "L216", "title": "EvaluationLink", "to": "N212", "width": 1}, {"arrows": "to", "from": "L217", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L217", "title": "ContextLink", "to": "L207", "width": 1}, {"arrows": "to", "from": "L218", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L218", "title": "ContextLink", "to": "L208", "width": 1}, {"arrows": "to", "from": "L219", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L219", "title": "ContextLink", "to": "L209", "width": 1}, {"arrows": "to", "from": "L220", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L220", "title": "ContextLink", "to": "L210", "width": 1}, {"arrows": "to", "from": "L221", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L221", "title": "ContextLink", "to": "L211", "width": 1}, {"arrows": "to", "from": "L222", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L222", "title": "ContextLink", "to": "L212", "width": 1}, {"arrows": "to", "from": "L223", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L223", "title": "ContextLink", "to": "L213", "width": 1}, {"arrows": "to", "from": "L224", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L224", "title": "ContextLink", "to": "L214", "width": 1}, {"arrows": "to", "from": "L225", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L225", "title": "ContextLink", "to": "L215", "width": 1}, {"arrows": "to", "from": "L226", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L226", "title": "ContextLink", "to": "L216", "width": 1}, {"arrows": "to", "from": "L227", "title": "EvaluationLink", "to": "N215", "width": 1}, {"arrows": "to", "from": "L227", "title": "EvaluationLink", "to": "N217", "width": 1}, {"arrows": "to", "from": "L227", "title": "EvaluationLink", "to": "N218", "width": 1}, {"arrows": "to", "from": "L227", "title": "EvaluationLink", "to": "N219", "width": 1}, {"arrows": "to", "from": "L227", "title": "EvaluationLink", "to": "N220", "width": 1}, {"arrows": "to", "from": "L227", "title": "EvaluationLink", "to": "N21", "width": 1}, {"arrows": "to", "from": "L227", "title": "EvaluationLink", "to": "N221", "width": 1}, {"arrows": "to", "from": "L227", "title": "EvaluationLink", "to": "N226", "width": 1}, {"arrows": "to", "from": "L228", "title": "EvaluationLink", "to": "N215", "width": 1}, {"arrows": "to", "from": "L228", "title": "EvaluationLink", "to": "N222", "width": 1}, {"arrows": "to", "from": "L228", "title": "EvaluationLink", "to": "N193", "width": 1}, {"arrows": "to", "from": "L228", "title": "EvaluationLink", "to": "N45", "width": 1}, {"arrows": "to", "from": "L229", "title": "EvaluationLink", "to": "N221", "width": 1}, {"arrows": "to", "from": "L229", "title": "EvaluationLink", "to": "N223", "width": 1}, {"arrows": "to", "from": "L229", "title": "EvaluationLink", "to": "N144", "width": 1}, {"arrows": "to", "from": "L229", "title": "EvaluationLink", "to": "N227", "width": 1}, {"arrows": "to", "from": "L230", "title": "EvaluationLink", "to": "N221", "width": 1}, {"arrows": "to", "from": "L230", "title": "EvaluationLink", "to": "N4", "width": 1}, {"arrows": "to", "from": "L230", "title": "EvaluationLink", "to": "N60", "width": 1}, {"arrows": "to", "from": "L231", "title": "EvaluationLink", "to": "N225", "width": 1}, {"arrows": "to", "from": "L231", "title": "EvaluationLink", "to": "N193", "width": 1}, {"arrows": "to", "from": "L231", "title": "EvaluationLink", "to": "N224", "width": 1}, {"arrows": "to", "from": "L231", "title": "EvaluationLink", "to": "N144", "width": 1}, {"arrows": "to", "from": "L231", "title": "EvaluationLink", "to": "N228", "width": 1}, {"arrows": "to", "from": "L232", "title": "EvaluationLink", "to": "N193", "width": 1}, {"arrows": "to", "from": "L232", "title": "EvaluationLink", "to": "N144", "width": 1}, {"arrows": "to", "from": "L232", "title": "EvaluationLink", "to": "N189", "width": 1}, {"arrows": "to", "from": "L233", "title": "EvaluationLink", "to": "N193", "width": 1}, {"arrows": "to", "from": "L233", "title": "EvaluationLink", "to": "N221", "width": 1}, {"arrows": "to", "from": "L233", "title": "EvaluationLink", "to": "N63", "width": 1}, {"arrows": "to", "from": "L234", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L234", "title": "ContextLink", "to": "L227", "width": 1}, {"arrows": "to", "from": "L235", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L235", "title": "ContextLink", "to": "L228", "width": 1}, {"arrows": "to", "from": "L236", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L236", "title": "ContextLink", "to": "L229", "width": 1}, {"arrows": "to", "from": "L237", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L237", "title": "ContextLink", "to": "L230", "width": 1}, {"arrows": "to", "from": "L238", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L238", "title": "ContextLink", "to": "L231", "width": 1}, {"arrows": "to", "from": "L239", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L239", "title": "ContextLink", "to": "L232", "width": 1}, {"arrows": "to", "from": "L240", "title": "ContextLink", "to": "N229", "width": 1}, {"arrows": "to", "from": "L240", "title": "ContextLink", "to": "L233", "width": 1}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {
    "configure": {
        "enabled": true,
        "filter": [
            "physics"
        ]
    },
    "edges": {
        "color": {
            "inherit": true
        },
        "smooth": {
            "enabled": true,
            "type": "dynamic"
        }
    },
    "interaction": {
        "dragNodes": true,
        "hideEdgesOnDrag": false,
        "hideNodesOnDrag": false
    },
    "physics": {
        "enabled": true,
        "stabilization": {
            "enabled": true,
            "fit": true,
            "iterations": 1000,
            "onlyDynamicEdges": false,
            "updateInterval": 50
        }
    }
};

                  


                  
                  // if this network requires displaying the configure window,
                  // put it in its div
                  options.configure["container"] = document.getElementById("config");
                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  
                      network.on("stabilizationProgress", function(params) {
                          document.getElementById('loadingBar').removeAttribute("style");
                          var maxWidth = 496;
                          var minWidth = 20;
                          var widthFactor = params.iterations/params.total;
                          var width = Math.max(minWidth,maxWidth * widthFactor);
                          document.getElementById('bar').style.width = width + 'px';
                          document.getElementById('text').innerHTML = Math.round(widthFactor*100) + '%';
                      });
                      network.once("stabilizationIterationsDone", function() {
                          document.getElementById('text').innerHTML = '100%';
                          document.getElementById('bar').style.width = '496px';
                          document.getElementById('loadingBar').style.opacity = 0;
                          // really clean the dom element
                          setTimeout(function () {document.getElementById('loadingBar').style.display = 'none';}, 500);
                      });
                  

                  return network;

              }
              drawGraph();
        </script>
    </body>
</html>