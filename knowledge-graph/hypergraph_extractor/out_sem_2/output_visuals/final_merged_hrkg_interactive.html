<html>
    <head>
        <meta charset="utf-8">
        
            <script>function neighbourhoodHighlight(params) {
  // console.log("in nieghbourhoodhighlight");
  allNodes = nodes.get({ returnType: "Object" });
  // originalNodes = JSON.parse(JSON.stringify(allNodes));
  // if something is selected:
  if (params.nodes.length > 0) {
    highlightActive = true;
    var i, j;
    var selectedNode = params.nodes[0];
    var degrees = 2;

    // mark all nodes as hard to read.
    for (let nodeId in allNodes) {
      // nodeColors[nodeId] = allNodes[nodeId].color;
      allNodes[nodeId].color = "rgba(200,200,200,0.5)";
      if (allNodes[nodeId].hiddenLabel === undefined) {
        allNodes[nodeId].hiddenLabel = allNodes[nodeId].label;
        allNodes[nodeId].label = undefined;
      }
    }
    var connectedNodes = network.getConnectedNodes(selectedNode);
    var allConnectedNodes = [];

    // get the second degree nodes
    for (i = 1; i < degrees; i++) {
      for (j = 0; j < connectedNodes.length; j++) {
        allConnectedNodes = allConnectedNodes.concat(
          network.getConnectedNodes(connectedNodes[j])
        );
      }
    }

    // all second degree nodes get a different color and their label back
    for (i = 0; i < allConnectedNodes.length; i++) {
      // allNodes[allConnectedNodes[i]].color = "pink";
      allNodes[allConnectedNodes[i]].color = "rgba(150,150,150,0.75)";
      if (allNodes[allConnectedNodes[i]].hiddenLabel !== undefined) {
        allNodes[allConnectedNodes[i]].label =
          allNodes[allConnectedNodes[i]].hiddenLabel;
        allNodes[allConnectedNodes[i]].hiddenLabel = undefined;
      }
    }

    // all first degree nodes get their own color and their label back
    for (i = 0; i < connectedNodes.length; i++) {
      // allNodes[connectedNodes[i]].color = undefined;
      allNodes[connectedNodes[i]].color = nodeColors[connectedNodes[i]];
      if (allNodes[connectedNodes[i]].hiddenLabel !== undefined) {
        allNodes[connectedNodes[i]].label =
          allNodes[connectedNodes[i]].hiddenLabel;
        allNodes[connectedNodes[i]].hiddenLabel = undefined;
      }
    }

    // the main node gets its own color and its label back.
    // allNodes[selectedNode].color = undefined;
    allNodes[selectedNode].color = nodeColors[selectedNode];
    if (allNodes[selectedNode].hiddenLabel !== undefined) {
      allNodes[selectedNode].label = allNodes[selectedNode].hiddenLabel;
      allNodes[selectedNode].hiddenLabel = undefined;
    }
  } else if (highlightActive === true) {
    // console.log("highlightActive was true");
    // reset all nodes
    for (let nodeId in allNodes) {
      // allNodes[nodeId].color = "purple";
      allNodes[nodeId].color = nodeColors[nodeId];
      // delete allNodes[nodeId].color;
      if (allNodes[nodeId].hiddenLabel !== undefined) {
        allNodes[nodeId].label = allNodes[nodeId].hiddenLabel;
        allNodes[nodeId].hiddenLabel = undefined;
      }
    }
    highlightActive = false;
  }

  // transform the object into an array
  var updateArray = [];
  if (params.nodes.length > 0) {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        // console.log(allNodes[nodeId]);
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  } else {
    // console.log("Nothing was selected");
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        // console.log(allNodes[nodeId]);
        // allNodes[nodeId].color = {};
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  }
}

function filterHighlight(params) {
  allNodes = nodes.get({ returnType: "Object" });
  // if something is selected:
  if (params.nodes.length > 0) {
    filterActive = true;
    let selectedNodes = params.nodes;

    // hiding all nodes and saving the label
    for (let nodeId in allNodes) {
      allNodes[nodeId].hidden = true;
      if (allNodes[nodeId].savedLabel === undefined) {
        allNodes[nodeId].savedLabel = allNodes[nodeId].label;
        allNodes[nodeId].label = undefined;
      }
    }

    for (let i=0; i < selectedNodes.length; i++) {
      allNodes[selectedNodes[i]].hidden = false;
      if (allNodes[selectedNodes[i]].savedLabel !== undefined) {
        allNodes[selectedNodes[i]].label = allNodes[selectedNodes[i]].savedLabel;
        allNodes[selectedNodes[i]].savedLabel = undefined;
      }
    }

  } else if (filterActive === true) {
    // reset all nodes
    for (let nodeId in allNodes) {
      allNodes[nodeId].hidden = false;
      if (allNodes[nodeId].savedLabel !== undefined) {
        allNodes[nodeId].label = allNodes[nodeId].savedLabel;
        allNodes[nodeId].savedLabel = undefined;
      }
    }
    filterActive = false;
  }

  // transform the object into an array
  var updateArray = [];
  if (params.nodes.length > 0) {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  } else {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  }
}

function selectNode(nodes) {
  network.selectNodes(nodes);
  neighbourhoodHighlight({ nodes: nodes });
  return nodes;
}

function selectNodes(nodes) {
  network.selectNodes(nodes);
  filterHighlight({nodes: nodes});
  return nodes;
}

function highlightFilter(filter) {
  let selectedNodes = []
  let selectedProp = filter['property']
  if (filter['item'] === 'node') {
    let allNodes = nodes.get({ returnType: "Object" });
    for (let nodeId in allNodes) {
      if (allNodes[nodeId][selectedProp] && filter['value'].includes((allNodes[nodeId][selectedProp]).toString())) {
        selectedNodes.push(nodeId)
      }
    }
  }
  else if (filter['item'] === 'edge'){
    let allEdges = edges.get({returnType: 'object'});
    // check if the selected property exists for selected edge and select the nodes connected to the edge
    for (let edge in allEdges) {
      if (allEdges[edge][selectedProp] && filter['value'].includes((allEdges[edge][selectedProp]).toString())) {
        selectedNodes.push(allEdges[edge]['from'])
        selectedNodes.push(allEdges[edge]['to'])
      }
    }
  }
  selectNodes(selectedNodes)
}</script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css" integrity="sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
            <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
            
            
            
            
            
            

        
<center>
<h1></h1>
</center>

<!-- <link rel="stylesheet" href="../node_modules/vis/dist/vis.min.css" type="text/css" />
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>-->
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/css/bootstrap.min.css"
          rel="stylesheet"
          integrity="sha384-eOJMYsd53ii+scO/bJGFsiCZc+5NDVN2yr8+0RDqr0Ql0h+rP48ckxlpbzKgwra6"
          crossorigin="anonymous"
        />
        <script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>


        <center>
          <h1></h1>
        </center>
        <style type="text/css">

             #mynetwork {
                 width: 100%;
                 height: 900px;
                 background-color: #ffffff;
                 border: 1px solid lightgray;
                 position: relative;
                 float: left;
             }

             
             #loadingBar {
                 position:absolute;
                 top:0px;
                 left:0px;
                 width: 100%;
                 height: 900px;
                 background-color:rgba(200,200,200,0.8);
                 -webkit-transition: all 0.5s ease;
                 -moz-transition: all 0.5s ease;
                 -ms-transition: all 0.5s ease;
                 -o-transition: all 0.5s ease;
                 transition: all 0.5s ease;
                 opacity:1;
             }

             #bar {
                 position:absolute;
                 top:0px;
                 left:0px;
                 width:20px;
                 height:20px;
                 margin:auto auto auto auto;
                 border-radius:11px;
                 border:2px solid rgba(30,30,30,0.05);
                 background: rgb(0, 173, 246); /* Old browsers */
                 box-shadow: 2px 0px 4px rgba(0,0,0,0.4);
             }

             #border {
                 position:absolute;
                 top:10px;
                 left:10px;
                 width:500px;
                 height:23px;
                 margin:auto auto auto auto;
                 box-shadow: 0px 0px 4px rgba(0,0,0,0.2);
                 border-radius:10px;
             }

             #text {
                 position:absolute;
                 top:8px;
                 left:530px;
                 width:30px;
                 height:50px;
                 margin:auto auto auto auto;
                 font-size:22px;
                 color: #000000;
             }

             div.outerBorder {
                 position:relative;
                 top:400px;
                 width:600px;
                 height:44px;
                 margin:auto auto auto auto;
                 border:8px solid rgba(0,0,0,0.1);
                 background: rgb(252,252,252); /* Old browsers */
                 background: -moz-linear-gradient(top,  rgba(252,252,252,1) 0%, rgba(237,237,237,1) 100%); /* FF3.6+ */
                 background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(252,252,252,1)), color-stop(100%,rgba(237,237,237,1))); /* Chrome,Safari4+ */
                 background: -webkit-linear-gradient(top,  rgba(252,252,252,1) 0%,rgba(237,237,237,1) 100%); /* Chrome10+,Safari5.1+ */
                 background: -o-linear-gradient(top,  rgba(252,252,252,1) 0%,rgba(237,237,237,1) 100%); /* Opera 11.10+ */
                 background: -ms-linear-gradient(top,  rgba(252,252,252,1) 0%,rgba(237,237,237,1) 100%); /* IE10+ */
                 background: linear-gradient(to bottom,  rgba(252,252,252,1) 0%,rgba(237,237,237,1) 100%); /* W3C */
                 filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#fcfcfc', endColorstr='#ededed',GradientType=0 ); /* IE6-9 */
                 border-radius:72px;
                 box-shadow: 0px 0px 10px rgba(0,0,0,0.2);
             }
             

             
             #config {
                 float: left;
                 width: 400px;
                 height: 600px;
             }
             

             
        </style>
    </head>


    <body>
        <div class="card" style="width: 100%">
            
            
            <div id="mynetwork" class="card-body"></div>
        </div>

        
            <div id="loadingBar">
              <div class="outerBorder">
                <div id="text">0%</div>
                <div id="border">
                  <div id="bar"></div>
                </div>
              </div>
            </div>
        
        
            <div id="config"></div>
        

        <script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "gold", "id": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "label": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "node_class": "Document", "shape": "star", "size": 25}, {"color": "lightgreen", "id": "SENT_1", "label": "Sentence 1", "node_class": "Sentence", "shape": "box", "size": 20, "title": "Abstract Liver enzymes are commonly used in the evaluation of patients with a\nrange of diseases. Classically Liver enzymes are used to give information on\nwhether a patient\u2019s primary disorder is hepatitic or cholestatic in origin.\nHowever, knowledge of enzyme ratios and pattern recognition allow much more\ninformation to be derived from Liver enzymes. This paper offers an insight to\ngeneralists on how to extract greater information from Liver enzymes in order to\nimprove the investigation and management of liver disease. Introduction Liver\nFunction Tests (LFTs) are one of the most commonlyrequested screening blood\ntests."}, {"color": "lightgreen", "id": "SENT_2", "label": "Sentence 2", "node_class": "Sentence", "shape": "box", "size": 20, "title": "Whether for the investigation of suspected liver disease, monitoring of disease\nactivity, or simply as \u2018routine\u2019 blood analysis, Liver Function Tests (LFTs) can\nprovide a host of information on a range of disease processes. The title \u2018liver\nfunction tests\u2019 is, however, somewhat of a misnomer; only the bilirubin and\nalbumin given in this panel offer information regarding the functional capacity\nof the liver. At a basic level the evaluation of liver enzymes simply gives\ninformation as to whether a patient\u2019s primary disorder is hepatitic or\ncholestatic in origin. However, much more may be interpreted from these assays\nwith knowledge of enzyme ratios and pattern recognition."}, {"color": "lightgreen", "id": "SENT_3", "label": "Sentence 3", "node_class": "Sentence", "shape": "box", "size": 20, "title": "This paper offers an insight to generalists of how to yield greater information\nfrom this simple test. Patterns and Use of Hepatic Enzymes in Practice The liver\nenzyme profile should always be assessed in conjunction with a thorough history\nand clinical examination. Despite a thorough history and clinical examination,\nthere are many occasions when doubt persists over an underlying diagnosis. For\nexample, does an overweight diabetic who enjoys a few glasses of wine at the\nweekend have alcoholic or non-alcoholic fatty liver disease? In such\ncircumstances the absolute liver enzyme levels and ratios may point the\nclinician in the right direction. Furthermore, the pattern of enzymes will\nassist, not only with differentiating between cholestasis and hepatitis, but\nwill aid diagnosis when there is a mixed picture."}, {"color": "lightgreen", "id": "SENT_4", "label": "Sentence 4", "node_class": "Sentence", "shape": "box", "size": 20, "title": "Understanding Cholestasis: Mechanical or Medical? Mechanical biliary obstruction\nresults in raised levels of ALP,GGT and often bilirubin. ALP will usually be\nmarkedly raised in comparison with ALT. Levels of ALP and GGT elevated in\nsimilar proportions signify a hepatobiliary source. Otherwise alternative causes\nof single enzyme elevation should be considered. When due to\ncholedocholithiasis, the levels of ALP and GGT tend to fluctuate (in comparison\nto stricture forming disease) and may be associated with a normal bilirubin.\nEnzyme titres tend to rise and fall gradually and may be preceded by a peaked\nrise in liver transaminases which can reach \u003e1000 I/U."}, {"color": "lightgreen", "id": "SENT_5", "label": "Sentence 5", "node_class": "Sentence", "shape": "box", "size": 20, "title": "The AST:ALT ratio (De Ritis ratio) may assist in differentiating the site of\nbiliary obstruction. When associated with a cholestatic picture, an AST:ALT\nratio of \u003c1.5 suggests an extrahepatic obstruction. In such circumstances the\nALT titre is frequently considerably higher than AST. An AST:ALT ratio of \u003e1.5\nindicates intrahepatic (mechanical or medical) cholestasis is more likely. Drug-\ninduced cholestasis usually presents with a preferential rise in ALP, rather\nthan GGT, or with an ALT:ALP ratio of \u003c2. Causative drugs would include:\nantibiotics, immunosuppressants, tricyclic antidepressants and angiotensin\nconverting enzyme inhibitors."}, {"color": "lightgreen", "id": "SENT_6", "label": "Sentence 6", "node_class": "Sentence", "shape": "box", "size": 20, "title": "In Primary Biliary Cirrhosis, an autoimmune condition of the intrahepatic\nbiliary ducts, the level of ALP is generally greater than that of GGT. In this\ncase, transaminases are invariably normal or only minimally elevated. Both the\nEuropean Association for Study of the Liver (EASL) and the American Association\nfor Study of Liver Disease (AASLD) recommend that a diagnosis of PBC may be\nbased on cholestatic liver enzyme levels in conjunction with the demonstration\nof antimitochondrial antibodies. If either of these two criteria is absent,\nimaging and liver biopsy become necessary."}, {"color": "lightgreen", "id": "SENT_7", "label": "Sentence 7", "node_class": "Sentence", "shape": "box", "size": 20, "title": "AST and ALP are used within some scoring criteria to monitor the effects of\nursodeoxycholic acid in the management of PBC. A recent study has shown that a\nraised AST:ALT ratio outperforms other non-histological indicators of cirrhosis\nin PBC, but still only achieves a low sensitivity and a specificity of 65-79%.\nAs with PBC, liver enzymes play a key role in the diagnosis of Primary\nSclerosing Cholangitis (PSC)."}, {"color": "lightgreen", "id": "SENT_8", "label": "Sentence 8", "node_class": "Sentence", "shape": "box", "size": 20, "title": "When other causes of liver disease have been excluded, a raised GGT, and\nparticularly ALP, are diagnostic when associated with typical Endoscopic\nRetrograde Cholangiopancreatography (ERCP) or Magnetic Resonance\nCholangiopancreatography (MRCP) findings. This can preclude the need for a liver\nbiopsy. Transaminase levels may be raised up to 2-3 times normal values in PSC\nbut this is not diagnostic. AST is a component of the Mayo Risk Score, which\ncalculates the risk of disease progression in PSC. A high Mayo Risk Score, and\nan AST:ALT ratio of \u003e1.12 have been shown to be indicators of risk for the\ndevelopment of oesophageal varices."}, {"color": "lightgreen", "id": "SENT_9", "label": "Sentence 9", "node_class": "Sentence", "shape": "box", "size": 20, "title": "In Primary Sclerosing Cholangitis (PSC), as with other liver diseases, there are\nsuggestions that an AST:ALT ratio of \u003e1 indicates the development of cirrhosis.\nAlcohol induces hepatic enzymes leading to a raised GGT with an ALP which may be\nnormal, or disproportionately lower than the GGT. A GGT:ALP ratio \u003e2.5 in\nassociation with jaundice suggests alcohol as a cause of liver disease. The\npresence of a macrocytosis, due to either an associated dietary deficiency of\nfolate or B12, or due to a direct suppression of bone marrow by alcohol is\nsupportive of the diagnosis of alcoholic liver disease."}, {"color": "lightgreen", "id": "SENT_10", "label": "Sentence 10", "node_class": "Sentence", "shape": "box", "size": 20, "title": "A raised GGT is not diagnostic of alcohol abuse, with research showing A raised\nGGT remains high in former drinkers as well as current drinkers. In men, the\nhighest levels of GGT occur in those who drink daily. In women, binge drinkers\nand those consuming alcohol without food will have especially high levels. The\nlevel of GGT is loosely dose dependant, with those in the top two quartiles of\nalcohol intake having the highest titres."}, {"color": "lightgreen", "id": "SENT_11", "label": "Sentence 11", "node_class": "Sentence", "shape": "box", "size": 20, "title": "The Fatty Liver and the AST:ALT Ratio During the last few decades there has been\nresearch into using the AST:ALT ratio in the differentiation of alcoholic liver\ndisease (ALD) from other forms of liver disease, particularly the Non-alcoholic\nFatty Liver Disease (NAFLD) spectrum. Both AST and ALT enzymes require\npyridoxal-5\u2019-phosphate (vitamin B6) to function properly. pyridoxal-5\u2019-phosphate\n(vitamin B6)\u0027s absence in nutritionally-deficient heavy-drinkers has a much\nlarger effect on the production of ALT than that of AST, causing the AST:ALT\nratio to rise. A normal AST:ALT ratio should be \u003c1."}, {"color": "lightgreen", "id": "SENT_12", "label": "Sentence 12", "node_class": "Sentence", "shape": "box", "size": 20, "title": "In patients with alcoholic liver disease, the AST:ALT ratio is \u003e1 in 92% of\npatients, and \u003e2 in 70%.13 AST:ALT scores \u003e2 are, therefore, strongly suggestive\nof alcoholic liver disease and scores \u003c1 more suggestive of the Non-alcoholic\nFatty Liver Disease (NAFLD) spectrum. High ratios reflect the severity of\nhepatitis or underlying liver disease rather than high alcohol consumption. This\nmeans that most heavy-drinkers will not have an AST: ALT ratio \u003e1 as most heavy-\ndrinkers have not yet developed alcoholic liver disease (ALD)."}, {"color": "lightgreen", "id": "SENT_13", "label": "Sentence 13", "node_class": "Sentence", "shape": "box", "size": 20, "title": "No studies have shown that the AST:ALT ratio, either alone or in combination\nwith other factors or models, has the necessary sensitivity or specificity to\ndefinitively differentiate between alcoholic liver disease (ALD) and NAFLD, but\nthe AST:ALT ratio acts as a useful clinical guide when considering the need for\nliver biopsy. It should also be noted that liver transaminases are known to\nworsen in response to cessation of alcohol intake (often coinciding with\nadmission to hospital) and that ALT has also been shown to rise simply from\nadmission to hospital, even in patients with no liver disease."}, {"color": "lightgreen", "id": "SENT_14", "label": "Sentence 14", "node_class": "Sentence", "shape": "box", "size": 20, "title": "Although models exist which exclude cirrhosis in NAFLD with reasonable accuracy,\nliver enzyme analysis has so far failed to provide a sensitive and specific\nenough means to make a diagnosis. At present liver biopsy cannot be avoided in\ncases where confirmation of NASH or cirrhosis is necessary. The role of liver\nenzyme analysis in NAFLD lies in both the early identification and modification\nof associated metabolic risk factors such as hypertension, hyperlipidaemia and\nglycaemic control and in risk stratification for the future."}, {"color": "lightgreen", "id": "SENT_15", "label": "Sentence 15", "node_class": "Sentence", "shape": "box", "size": 20, "title": "A scoring system developed at the Mayo clinic uses age, hyperglycemia, body mass\nindex, platelet count, albumin, and AST:ALT ratio to accurately differentiate\npatients with advanced fibrosis in NAFLD. the AST:ALT Ratio becomes considerably\nless specific in determining underlying disease with the development of\ncirrhosis, as the AST:ALT Ratio will increase across a broad range of diseases.\nthe AST:ALT Ratio is, however, useful in NAFLD patients known not to be abusing\nalcohol as a score of \u003e1 should lead to the consideration that NAFLD patients\nknown not to be abusing alcohol may have developed cirrhosis."}, {"color": "lightcoral", "id": "F1_SENT_1_input_txt", "label": "F1_SENT_1_input_txt: used_for", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: used_for\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E1", "label": "Liver enzymes", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Liver enzymes\u003cbr\u003eType: Biomarker"}, {"color": "skyblue", "id": "E2", "label": "Evaluation", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Evaluation\u003cbr\u003eType: ClinicalAction"}, {"color": "skyblue", "id": "E3", "label": "Disease", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Disease\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F2_SENT_1_input_txt", "label": "F2_SENT_1_input_txt: used_for", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: used_for\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E4", "label": "Hepatitic disorder", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Hepatitic disorder\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F3_SENT_1_input_txt", "label": "F3_SENT_1_input_txt: used_for", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: used_for\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E5", "label": "Cholestatic disorder", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Cholestatic disorder\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F4_SENT_1_input_txt", "label": "F4_SENT_1_input_txt: enables", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: enables\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E6", "label": "Enzyme ratios", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Enzyme ratios\u003cbr\u003eType: Biomarker"}, {"color": "skyblue", "id": "E7", "label": "Pattern recognition", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Pattern recognition\u003cbr\u003eType: Concept"}, {"color": "lightcoral", "id": "F5_SENT_1_input_txt", "label": "F5_SENT_1_input_txt: requires_investigation", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: requires_investigation\u003cbr\u003eTruth: 0.6"}, {"color": "skyblue", "id": "E9", "label": "Investigation", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Investigation\u003cbr\u003eType: ClinicalAction"}, {"color": "lightcoral", "id": "F6_SENT_1_input_txt", "label": "F6_SENT_1_input_txt: improves", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: improves\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E8", "label": "Liver disease", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Liver disease\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F7_SENT_1_input_txt", "label": "F7_SENT_1_input_txt: improves", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: improves\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E10", "label": "Management", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Management\u003cbr\u003eType: ClinicalAction"}, {"color": "lightcoral", "id": "F8_SENT_1_input_txt", "label": "F8_SENT_1_input_txt: is_a", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: is_a\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E11", "label": "Liver Function Tests", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Liver Function Tests\u003cbr\u003eType: Biomarker"}, {"color": "lightcoral", "id": "F9_SENT_1_input_txt", "label": "F9_SENT_1_input_txt: used_for", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: used_for\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E12", "label": "Screening blood tests", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Screening blood tests\u003cbr\u003eType: ClinicalAction"}, {"color": "lightcoral", "id": "F10_SENT_2_input_txt", "label": "F10_SENT_2_input_txt: provides_information_on", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: provides_information_on\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F11_SENT_2_input_txt", "label": "F11_SENT_2_input_txt: provides_information_on", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: provides_information_on\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E13", "label": "Disease activity", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Disease activity\u003cbr\u003eType: Endpoint"}, {"color": "lightcoral", "id": "F12_SENT_2_input_txt", "label": "F12_SENT_2_input_txt: provides_information_on", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: provides_information_on\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E14", "label": "Blood analysis", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Blood analysis\u003cbr\u003eType: ClinicalAction"}, {"color": "lightcoral", "id": "F13_SENT_2_input_txt", "label": "F13_SENT_2_input_txt: provides_information_on", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: provides_information_on\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E15", "label": "Bilirubin", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Bilirubin\u003cbr\u003eType: Biomarker"}, {"color": "skyblue", "id": "E17", "label": "Functional capacity of the liver", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Functional capacity of the liver\u003cbr\u003eType: Endpoint"}, {"color": "lightcoral", "id": "F14_SENT_2_input_txt", "label": "F14_SENT_2_input_txt: provides_information_on", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: provides_information_on\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E16", "label": "Albumin", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Albumin\u003cbr\u003eType: Biomarker"}, {"color": "lightcoral", "id": "F15_SENT_2_input_txt", "label": "F15_SENT_2_input_txt: provides_information_on", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: provides_information_on\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F16_SENT_2_input_txt", "label": "F16_SENT_2_input_txt: provides_information_on", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: provides_information_on\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F17_SENT_2_input_txt", "label": "F17_SENT_2_input_txt: requires_investigation", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: requires_investigation\u003cbr\u003eTruth: 0.6"}, {"color": "lightcoral", "id": "F18_SENT_2_input_txt", "label": "F18_SENT_2_input_txt: implies", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: implies\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F136_SENT_15_input_txt", "label": "F136_SENT_15_input_txt: implies", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: implies\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F128_SENT_14_input_txt", "label": "F128_SENT_14_input_txt: associated_with", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: associated_with\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F19_SENT_3_input_txt", "label": "F19_SENT_3_input_txt: requires_investigation", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: requires_investigation\u003cbr\u003eTruth: 0.6"}, {"color": "skyblue", "id": "E18", "label": "Liver enzyme profile", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Liver enzyme profile\u003cbr\u003eType: Biomarker"}, {"color": "lightcoral", "id": "F20_SENT_3_input_txt", "label": "F20_SENT_3_input_txt: requires_investigation", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: requires_investigation\u003cbr\u003eTruth: 0.6"}, {"color": "skyblue", "id": "E25", "label": "Absolute liver enzyme levels", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Absolute liver enzyme levels\u003cbr\u003eType: Biomarker"}, {"color": "lightcoral", "id": "F21_SENT_3_input_txt", "label": "F21_SENT_3_input_txt: requires_investigation", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: requires_investigation\u003cbr\u003eTruth: 0.6"}, {"color": "skyblue", "id": "E26", "label": "Liver enzyme ratios", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Liver enzyme ratios\u003cbr\u003eType: Biomarker"}, {"color": "lightcoral", "id": "F22_SENT_3_input_txt", "label": "F22_SENT_3_input_txt: requires_investigation", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: requires_investigation\u003cbr\u003eTruth: 0.6"}, {"color": "skyblue", "id": "E27", "label": "Pattern of enzymes", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Pattern of enzymes\u003cbr\u003eType: Biomarker"}, {"color": "lightcoral", "id": "F23_SENT_3_input_txt", "label": "F23_SENT_3_input_txt: implies", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: implies\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F132_SENT_15_input_txt", "label": "F132_SENT_15_input_txt: specificity_decreases", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: specificity_decreases\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F133_SENT_15_input_txt", "label": "F133_SENT_15_input_txt: level_increases", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: level_increases\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F24_SENT_4_input_txt", "label": "F24_SENT_4_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E29", "label": "Mechanical biliary obstruction", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Mechanical biliary obstruction\u003cbr\u003eType: Disease"}, {"color": "skyblue", "id": "E30", "label": "ALP", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: ALP\u003cbr\u003eType: Biomarker"}, {"color": "lightcoral", "id": "F25_SENT_4_input_txt", "label": "F25_SENT_4_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E31", "label": "GGT", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: GGT\u003cbr\u003eType: Biomarker"}, {"color": "lightcoral", "id": "F26_SENT_4_input_txt", "label": "F26_SENT_4_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F27_SENT_4_input_txt", "label": "F27_SENT_4_input_txt: observational", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: observational\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E32", "label": "ALT", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: ALT\u003cbr\u003eType: Biomarker"}, {"color": "lightcoral", "id": "F28_SENT_4_input_txt", "label": "F28_SENT_4_input_txt: observational", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: observational\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F29_SENT_4_input_txt", "label": "F29_SENT_4_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E33", "label": "Hepatobiliary source", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Hepatobiliary source\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F30_SENT_4_input_txt", "label": "F30_SENT_4_input_txt: requires_investigation", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: requires_investigation\u003cbr\u003eTruth: 0.5"}, {"color": "skyblue", "id": "E34", "label": "Single enzyme elevation", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Single enzyme elevation\u003cbr\u003eType: Concept"}, {"color": "lightcoral", "id": "F31_SENT_4_input_txt", "label": "F31_SENT_4_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E36", "label": "Choledocholithiasis", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Choledocholithiasis\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F32_SENT_4_input_txt", "label": "F32_SENT_4_input_txt: observational", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: observational\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F33_SENT_4_input_txt", "label": "F33_SENT_4_input_txt: observational", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: observational\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F34_SENT_4_input_txt", "label": "F34_SENT_4_input_txt: observational", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: observational\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E38", "label": "Liver transaminases", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Liver transaminases\u003cbr\u003eType: Biomarker"}, {"color": "lightcoral", "id": "F35_SENT_5_input_txt", "label": "F35_SENT_5_input_txt: requires_investigation", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: requires_investigation\u003cbr\u003eTruth: 0.6"}, {"color": "skyblue", "id": "E39", "label": "AST:ALT ratio", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: AST:ALT ratio\u003cbr\u003eType: Biomarker"}, {"color": "lightcoral", "id": "F36_SENT_5_input_txt", "label": "F36_SENT_5_input_txt: implies", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: implies\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F131_SENT_15_input_txt", "label": "F131_SENT_15_input_txt: differentiates", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: differentiates\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F37_SENT_5_input_txt", "label": "F37_SENT_5_input_txt: observational_association", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: observational_association\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E42", "label": "AST:ALT ratio \u003c1.5", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: AST:ALT ratio \u003c1.5\u003cbr\u003eType: Biomarker"}, {"color": "skyblue", "id": "E41", "label": "Cholestatic picture", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Cholestatic picture\u003cbr\u003eType: Phenotype"}, {"color": "skyblue", "id": "E43", "label": "Extrahepatic obstruction", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Extrahepatic obstruction\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F38_SENT_5_input_txt", "label": "F38_SENT_5_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F39_SENT_5_input_txt", "label": "F39_SENT_5_input_txt: observational_association", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: observational_association\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E44", "label": "ALT titre", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: ALT titre\u003cbr\u003eType: Biomarker"}, {"color": "skyblue", "id": "E45", "label": "AST", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: AST\u003cbr\u003eType: Biomarker"}, {"color": "lightcoral", "id": "F40_SENT_5_input_txt", "label": "F40_SENT_5_input_txt: observational_association", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: observational_association\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E46", "label": "AST:ALT ratio \u003e1.5", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: AST:ALT ratio \u003e1.5\u003cbr\u003eType: Biomarker"}, {"color": "skyblue", "id": "E47", "label": "Intrahepatic cholestasis", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Intrahepatic cholestasis\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F41_SENT_5_input_txt", "label": "F41_SENT_5_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F42_SENT_5_input_txt", "label": "F42_SENT_5_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E48", "label": "Mechanical cholestasis", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Mechanical cholestasis\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F43_SENT_5_input_txt", "label": "F43_SENT_5_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E49", "label": "Medical cholestasis", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Medical cholestasis\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F44_SENT_5_input_txt", "label": "F44_SENT_5_input_txt: observational_association", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: observational_association\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E50", "label": "Drug-induced cholestasis", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Drug-induced cholestasis\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F45_SENT_5_input_txt", "label": "F45_SENT_5_input_txt: observational_association", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: observational_association\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F46_SENT_5_input_txt", "label": "F46_SENT_5_input_txt: observational_association", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: observational_association\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E51", "label": "ALT:ALP ratio \u003c2", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: ALT:ALP ratio \u003c2\u003cbr\u003eType: Biomarker"}, {"color": "lightcoral", "id": "F47_SENT_5_input_txt", "label": "F47_SENT_5_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E52", "label": "Antibiotics", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Antibiotics\u003cbr\u003eType: Drug"}, {"color": "lightcoral", "id": "F48_SENT_5_input_txt", "label": "F48_SENT_5_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E53", "label": "Immunosuppressants", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Immunosuppressants\u003cbr\u003eType: Drug"}, {"color": "lightcoral", "id": "F49_SENT_5_input_txt", "label": "F49_SENT_5_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E54", "label": "Tricyclic antidepressants", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Tricyclic antidepressants\u003cbr\u003eType: Drug"}, {"color": "lightcoral", "id": "F50_SENT_5_input_txt", "label": "F50_SENT_5_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E55", "label": "Angiotensin converting enzyme inhibitors", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Angiotensin converting enzyme inhibitors\u003cbr\u003eType: Drug"}, {"color": "lightcoral", "id": "F51_SENT_6_input_txt", "label": "F51_SENT_6_input_txt: observed_in", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: observed_in\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E56", "label": "Primary Biliary Cirrhosis", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Primary Biliary Cirrhosis\u003cbr\u003eType: Disease"}, {"color": "skyblue", "id": "E57", "label": "intrahepatic biliary ducts", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: intrahepatic biliary ducts\u003cbr\u003eType: AnatomicalStructure"}, {"color": "lightcoral", "id": "F52_SENT_6_input_txt", "label": "F52_SENT_6_input_txt: greater_than", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: greater_than\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F53_SENT_6_input_txt", "label": "F53_SENT_6_input_txt: normal_or_minimally_elevated", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: normal_or_minimally_elevated\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E58", "label": "transaminases", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: transaminases\u003cbr\u003eType: Biomarker"}, {"color": "lightcoral", "id": "F54_SENT_6_input_txt", "label": "F54_SENT_6_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F55_SENT_6_input_txt", "label": "F55_SENT_6_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F56_SENT_6_input_txt", "label": "F56_SENT_6_input_txt: recommends", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: recommends\u003cbr\u003eTruth: 0.6"}, {"color": "skyblue", "id": "E64", "label": "European Association for Study of the Liver", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: European Association for Study of the Liver\u003cbr\u003eType: Organization"}, {"color": "skyblue", "id": "E65", "label": "American Association for Study of Liver Disease", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: American Association for Study of Liver Disease\u003cbr\u003eType: Organization"}, {"color": "skyblue", "id": "E61", "label": "diagnosis of PBC", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: diagnosis of PBC\u003cbr\u003eType: ClinicalAction"}, {"color": "skyblue", "id": "E59", "label": "cholestatic liver enzyme levels", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: cholestatic liver enzyme levels\u003cbr\u003eType: Biomarker"}, {"color": "skyblue", "id": "E60", "label": "antimitochondrial antibodies", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: antimitochondrial antibodies\u003cbr\u003eType: Biomarker"}, {"color": "lightcoral", "id": "F57_SENT_6_input_txt", "label": "F57_SENT_6_input_txt: requires_investigation", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: requires_investigation\u003cbr\u003eTruth: 0.6"}, {"color": "skyblue", "id": "E62", "label": "imaging", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: imaging\u003cbr\u003eType: ClinicalAction"}, {"color": "skyblue", "id": "E63", "label": "liver biopsy", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: liver biopsy\u003cbr\u003eType: ClinicalAction"}, {"color": "lightcoral", "id": "F58_SENT_6_input_txt", "label": "F58_SENT_6_input_txt: implies", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: implies\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F59_SENT_7_input_txt", "label": "F59_SENT_7_input_txt: requires_monitoring", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: requires_monitoring\u003cbr\u003eTruth: 0.6"}, {"color": "skyblue", "id": "E66", "label": "ursodeoxycholic acid", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: ursodeoxycholic acid\u003cbr\u003eType: Drug"}, {"color": "skyblue", "id": "E67", "label": "PBC", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: PBC\u003cbr\u003eType: Disease"}, {"color": "skyblue", "id": "E72", "label": "scoring criteria", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: scoring criteria\u003cbr\u003eType: ClinicalAction"}, {"color": "lightcoral", "id": "F60_SENT_7_input_txt", "label": "F60_SENT_7_input_txt: observational", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: observational\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E68", "label": "non-histological indicators of cirrhosis", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: non-histological indicators of cirrhosis\u003cbr\u003eType: Biomarker"}, {"color": "skyblue", "id": "E69", "label": "cirrhosis", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: cirrhosis\u003cbr\u003eType: Disease"}, {"color": "skyblue", "id": "E74", "label": "recent study", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: recent study\u003cbr\u003eType: Study"}, {"color": "lightcoral", "id": "F61_SENT_7_input_txt", "label": "F61_SENT_7_input_txt: observational", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: observational\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F62_SENT_7_input_txt", "label": "F62_SENT_7_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F63_SENT_7_input_txt", "label": "F63_SENT_7_input_txt: requires_monitoring", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: requires_monitoring\u003cbr\u003eTruth: 0.6"}, {"color": "skyblue", "id": "E70", "label": "liver enzymes", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: liver enzymes\u003cbr\u003eType: Biomarker"}, {"color": "skyblue", "id": "E73", "label": "diagnosis", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: diagnosis\u003cbr\u003eType: ClinicalAction"}, {"color": "skyblue", "id": "E71", "label": "Primary Sclerosing Cholangitis", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Primary Sclerosing Cholangitis\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F64_SENT_8_input_txt", "label": "F64_SENT_8_input_txt: diagnostic_for", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: diagnostic_for\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E79", "label": "PSC", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: PSC\u003cbr\u003eType: Disease"}, {"color": "skyblue", "id": "E75", "label": "Endoscopic Retrograde Cholangiopancreatography", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Endoscopic Retrograde Cholangiopancreatography\u003cbr\u003eType: ClinicalAction"}, {"color": "skyblue", "id": "E76", "label": "Magnetic Resonance Cholangiopancreatography", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Magnetic Resonance Cholangiopancreatography\u003cbr\u003eType: ClinicalAction"}, {"color": "lightcoral", "id": "F65_SENT_8_input_txt", "label": "F65_SENT_8_input_txt: precludes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: precludes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E77", "label": "Liver biopsy", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Liver biopsy\u003cbr\u003eType: ClinicalAction"}, {"color": "lightcoral", "id": "F66_SENT_8_input_txt", "label": "F66_SENT_8_input_txt: not_diagnostic_for", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: not_diagnostic_for\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E78", "label": "Transaminase", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Transaminase\u003cbr\u003eType: Biomarker"}, {"color": "lightcoral", "id": "F67_SENT_8_input_txt", "label": "F67_SENT_8_input_txt: component_of", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: component_of\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E80", "label": "Mayo Risk Score", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Mayo Risk Score\u003cbr\u003eType: Endpoint"}, {"color": "lightcoral", "id": "F68_SENT_8_input_txt", "label": "F68_SENT_8_input_txt: calculates", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: calculates\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E81", "label": "Disease progression in PSC", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Disease progression in PSC\u003cbr\u003eType: Endpoint"}, {"color": "lightcoral", "id": "F69_SENT_8_input_txt", "label": "F69_SENT_8_input_txt: indicator_of", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: indicator_of\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E82", "label": "Oesophageal varices", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Oesophageal varices\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F70_SENT_8_input_txt", "label": "F70_SENT_8_input_txt: indicator_of", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: indicator_of\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F71_SENT_9_input_txt", "label": "F71_SENT_9_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E84", "label": "Cirrhosis", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Cirrhosis\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F72_SENT_9_input_txt", "label": "F72_SENT_9_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E85", "label": "Alcohol", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Alcohol\u003cbr\u003eType: Drug"}, {"color": "skyblue", "id": "E86", "label": "Hepatic enzymes", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Hepatic enzymes\u003cbr\u003eType: Biomarker"}, {"color": "lightcoral", "id": "F73_SENT_9_input_txt", "label": "F73_SENT_9_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F74_SENT_9_input_txt", "label": "F74_SENT_9_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F75_SENT_9_input_txt", "label": "F75_SENT_9_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E87", "label": "GGT:ALP ratio", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: GGT:ALP ratio\u003cbr\u003eType: Biomarker"}, {"color": "lightcoral", "id": "F76_SENT_9_input_txt", "label": "F76_SENT_9_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E88", "label": "Jaundice", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Jaundice\u003cbr\u003eType: AdverseEvent"}, {"color": "lightcoral", "id": "F77_SENT_9_input_txt", "label": "F77_SENT_9_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F78_SENT_9_input_txt", "label": "F78_SENT_9_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E90", "label": "Folate deficiency", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Folate deficiency\u003cbr\u003eType: Disease"}, {"color": "skyblue", "id": "E89", "label": "Macrocytosis", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Macrocytosis\u003cbr\u003eType: Biomarker"}, {"color": "lightcoral", "id": "F79_SENT_9_input_txt", "label": "F79_SENT_9_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E91", "label": "Vitamin B12 deficiency", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Vitamin B12 deficiency\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F80_SENT_9_input_txt", "label": "F80_SENT_9_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E92", "label": "Bone marrow suppression", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Bone marrow suppression\u003cbr\u003eType: AdverseEvent"}, {"color": "lightcoral", "id": "F81_SENT_9_input_txt", "label": "F81_SENT_9_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F82_SENT_9_input_txt", "label": "F82_SENT_9_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E93", "label": "Alcoholic liver disease", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Alcoholic liver disease\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F83_SENT_10_input_txt", "label": "F83_SENT_10_input_txt: not_diagnostic_of", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: not_diagnostic_of\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E94", "label": "Alcohol abuse", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Alcohol abuse\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F84_SENT_10_input_txt", "label": "F84_SENT_10_input_txt: remains_high_in", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: remains_high_in\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E95", "label": "Former drinkers", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Former drinkers\u003cbr\u003eType: Cohort"}, {"color": "lightcoral", "id": "F85_SENT_10_input_txt", "label": "F85_SENT_10_input_txt: remains_high_in", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: remains_high_in\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E96", "label": "Current drinkers", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Current drinkers\u003cbr\u003eType: Cohort"}, {"color": "lightcoral", "id": "F86_SENT_10_input_txt", "label": "F86_SENT_10_input_txt: highest_levels_in", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: highest_levels_in\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E97", "label": "Men", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Men\u003cbr\u003eType: Cohort"}, {"color": "skyblue", "id": "E98", "label": "Daily alcohol consumption", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Daily alcohol consumption\u003cbr\u003eType: Exposure"}, {"color": "lightcoral", "id": "F87_SENT_10_input_txt", "label": "F87_SENT_10_input_txt: especially_high_levels_in", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: especially_high_levels_in\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E99", "label": "Women", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Women\u003cbr\u003eType: Cohort"}, {"color": "skyblue", "id": "E100", "label": "Binge drinking", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Binge drinking\u003cbr\u003eType: Exposure"}, {"color": "lightcoral", "id": "F88_SENT_10_input_txt", "label": "F88_SENT_10_input_txt: especially_high_levels_in", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: especially_high_levels_in\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E101", "label": "Alcohol consumption without food", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Alcohol consumption without food\u003cbr\u003eType: Exposure"}, {"color": "lightcoral", "id": "F89_SENT_10_input_txt", "label": "F89_SENT_10_input_txt: dose_dependence", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: dose_dependence\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E102", "label": "Alcohol intake (top two quartiles)", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Alcohol intake (top two quartiles)\u003cbr\u003eType: Exposure"}, {"color": "lightcoral", "id": "F90_SENT_10_input_txt", "label": "F90_SENT_10_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F91_SENT_10_input_txt", "label": "F91_SENT_10_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F92_SENT_10_input_txt", "label": "F92_SENT_10_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F93_SENT_10_input_txt", "label": "F93_SENT_10_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F94_SENT_11_input_txt", "label": "F94_SENT_11_input_txt: used_for_differentiation", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: used_for_differentiation\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E103", "label": "Non-alcoholic Fatty Liver Disease", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Non-alcoholic Fatty Liver Disease\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F95_SENT_11_input_txt", "label": "F95_SENT_11_input_txt: requires", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: requires\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E104", "label": "pyridoxal-5\u2019-phosphate (vitamin B6)", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: pyridoxal-5\u2019-phosphate (vitamin B6)\u003cbr\u003eType: Drug"}, {"color": "lightcoral", "id": "F96_SENT_11_input_txt", "label": "F96_SENT_11_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E108", "label": "Absence of pyridoxal-5\u2019-phosphate (vitamin B6)", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Absence of pyridoxal-5\u2019-phosphate (vitamin B6)\u003cbr\u003eType: Condition"}, {"color": "skyblue", "id": "E105", "label": "Nutritionally-deficient heavy-drinkers", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Nutritionally-deficient heavy-drinkers\u003cbr\u003eType: Cohort"}, {"color": "skyblue", "id": "E106", "label": "ALT production", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: ALT production\u003cbr\u003eType: Endpoint"}, {"color": "lightcoral", "id": "F97_SENT_11_input_txt", "label": "F97_SENT_11_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E107", "label": "AST production", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: AST production\u003cbr\u003eType: Endpoint"}, {"color": "lightcoral", "id": "F98_SENT_11_input_txt", "label": "F98_SENT_11_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F99_SENT_11_input_txt", "label": "F99_SENT_11_input_txt: requires_investigation", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: requires_investigation\u003cbr\u003eTruth: 0.6"}, {"color": "skyblue", "id": "E109", "label": "Normal AST:ALT ratio \u003c1", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Normal AST:ALT ratio \u003c1\u003cbr\u003eType: Concept"}, {"color": "lightcoral", "id": "F100_SENT_11_input_txt", "label": "F100_SENT_11_input_txt: implies", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: implies\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F135_SENT_15_input_txt", "label": "F135_SENT_15_input_txt: requires_investigation", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: requires_investigation\u003cbr\u003eTruth: 0.6"}, {"color": "lightcoral", "id": "F101_SENT_12_input_txt", "label": "F101_SENT_12_input_txt: observed_in", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: observed_in\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F102_SENT_12_input_txt", "label": "F102_SENT_12_input_txt: observed_in", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: observed_in\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F103_SENT_12_input_txt", "label": "F103_SENT_12_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F104_SENT_12_input_txt", "label": "F104_SENT_12_input_txt: suggests", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: suggests\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F105_SENT_12_input_txt", "label": "F105_SENT_12_input_txt: suggests", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: suggests\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F106_SENT_12_input_txt", "label": "F106_SENT_12_input_txt: reflects", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: reflects\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E23", "label": "Hepatitis", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Hepatitis\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F107_SENT_12_input_txt", "label": "F107_SENT_12_input_txt: reflects", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: reflects\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F108_SENT_12_input_txt", "label": "F108_SENT_12_input_txt: not_reflects", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: not_reflects\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E110", "label": "Alcohol consumption", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Alcohol consumption\u003cbr\u003eType: Exposure"}, {"color": "lightcoral", "id": "F109_SENT_12_input_txt", "label": "F109_SENT_12_input_txt: observed_in", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: observed_in\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E111", "label": "Heavy-drinker", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Heavy-drinker\u003cbr\u003eType: Cohort"}, {"color": "lightcoral", "id": "F110_SENT_12_input_txt", "label": "F110_SENT_12_input_txt: implies", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: implies\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F134_SENT_15_input_txt", "label": "F134_SENT_15_input_txt: useful_for", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: useful_for\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F111_SENT_12_input_txt", "label": "F111_SENT_12_input_txt: implies", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: implies\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F112_SENT_12_input_txt", "label": "F112_SENT_12_input_txt: implies", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: implies\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F130_SENT_14_input_txt", "label": "F130_SENT_14_input_txt: associated_with", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: associated_with\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F129_SENT_14_input_txt", "label": "F129_SENT_14_input_txt: associated_with", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: associated_with\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F113_SENT_13_input_txt", "label": "F113_SENT_13_input_txt: not_observed", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: not_observed\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E113", "label": "Sensitivity", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Sensitivity\u003cbr\u003eType: Endpoint"}, {"color": "skyblue", "id": "E114", "label": "Specificity", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Specificity\u003cbr\u003eType: Endpoint"}, {"color": "skyblue", "id": "E112", "label": "NAFLD", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: NAFLD\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F114_SENT_13_input_txt", "label": "F114_SENT_13_input_txt: not_observed", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: not_observed\u003cbr\u003eTruth: 0.9"}, {"color": "lightcoral", "id": "F115_SENT_13_input_txt", "label": "F115_SENT_13_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "skyblue", "id": "E118", "label": "Clinical guide for biopsy need", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Clinical guide for biopsy need\u003cbr\u003eType: Concept"}, {"color": "lightcoral", "id": "F116_SENT_13_input_txt", "label": "F116_SENT_13_input_txt: requires_investigation", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: requires_investigation\u003cbr\u003eTruth: 0.6"}, {"color": "lightcoral", "id": "F117_SENT_13_input_txt", "label": "F117_SENT_13_input_txt: observed", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: observed\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E115", "label": "Cessation of alcohol intake", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Cessation of alcohol intake\u003cbr\u003eType: ClinicalAction"}, {"color": "lightcoral", "id": "F118_SENT_13_input_txt", "label": "F118_SENT_13_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F119_SENT_13_input_txt", "label": "F119_SENT_13_input_txt: observed", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: observed\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E116", "label": "Admission to hospital", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Admission to hospital\u003cbr\u003eType: ClinicalAction"}, {"color": "lightcoral", "id": "F120_SENT_13_input_txt", "label": "F120_SENT_13_input_txt: causes", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: causes\u003cbr\u003eTruth: [0.85, 0.7]"}, {"color": "lightcoral", "id": "F121_SENT_13_input_txt", "label": "F121_SENT_13_input_txt: observed", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: observed\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E117", "label": "No liver disease", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: No liver disease\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F122_SENT_14_input_txt", "label": "F122_SENT_14_input_txt: fails_to_provide", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: fails_to_provide\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E119", "label": "Liver enzyme analysis", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Liver enzyme analysis\u003cbr\u003eType: Biomarker"}, {"color": "skyblue", "id": "E120", "label": "Diagnosis of cirrhosis in NAFLD", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Diagnosis of cirrhosis in NAFLD\u003cbr\u003eType: ClinicalAction"}, {"color": "lightcoral", "id": "F123_SENT_14_input_txt", "label": "F123_SENT_14_input_txt: requires_investigation", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: requires_investigation\u003cbr\u003eTruth: 0.6"}, {"color": "skyblue", "id": "E121", "label": "NASH", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: NASH\u003cbr\u003eType: Disease"}, {"color": "lightcoral", "id": "F124_SENT_14_input_txt", "label": "F124_SENT_14_input_txt: requires_investigation", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: requires_investigation\u003cbr\u003eTruth: 0.6"}, {"color": "lightcoral", "id": "F125_SENT_14_input_txt", "label": "F125_SENT_14_input_txt: role_in", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: role_in\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E122", "label": "Early identification of metabolic risk factors", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Early identification of metabolic risk factors\u003cbr\u003eType: ClinicalAction"}, {"color": "lightcoral", "id": "F126_SENT_14_input_txt", "label": "F126_SENT_14_input_txt: role_in", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: role_in\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E123", "label": "Modification of metabolic risk factors", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Modification of metabolic risk factors\u003cbr\u003eType: ClinicalAction"}, {"color": "lightcoral", "id": "F127_SENT_14_input_txt", "label": "F127_SENT_14_input_txt: role_in", "node_class": "Fact", "shape": "square", "size": 10, "title": "Fact: role_in\u003cbr\u003eTruth: 0.9"}, {"color": "skyblue", "id": "E124", "label": "Risk stratification", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Risk stratification\u003cbr\u003eType: ClinicalAction"}, {"color": "skyblue", "id": "E125", "label": "Hypertension", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Hypertension\u003cbr\u003eType: Disease"}, {"color": "skyblue", "id": "E126", "label": "Hyperlipidaemia", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Hyperlipidaemia\u003cbr\u003eType: Disease"}, {"color": "skyblue", "id": "E127", "label": "Glycaemic control", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Glycaemic control\u003cbr\u003eType: Endpoint"}, {"color": "skyblue", "id": "E128", "label": "Scoring system", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Scoring system\u003cbr\u003eType: ClinicalTool"}, {"color": "skyblue", "id": "E134", "label": "Advanced fibrosis", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Advanced fibrosis\u003cbr\u003eType: Disease"}, {"color": "skyblue", "id": "E130", "label": "Age", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Age\u003cbr\u003eType: Biomarker"}, {"color": "skyblue", "id": "E131", "label": "Hyperglycemia", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Hyperglycemia\u003cbr\u003eType: Biomarker"}, {"color": "skyblue", "id": "E132", "label": "Body mass index", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Body mass index\u003cbr\u003eType: Biomarker"}, {"color": "skyblue", "id": "E133", "label": "Platelet count", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Platelet count\u003cbr\u003eType: Biomarker"}, {"color": "skyblue", "id": "E135", "label": "Underlying disease", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Underlying disease\u003cbr\u003eType: Disease"}, {"color": "skyblue", "id": "E136", "label": "Score \u003e1", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Score \u003e1\u003cbr\u003eType: Threshold"}, {"color": "skyblue", "id": "E22", "label": "Cholestasis", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Cholestasis\u003cbr\u003eType: Disease"}, {"color": "skyblue", "id": "E129", "label": "Mayo clinic", "node_class": "Entity", "shape": "dot", "size": 15, "title": "Entity: Mayo clinic\u003cbr\u003eType: Institution"}]);
                  edges = new vis.DataSet([{"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "SENT_1", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "SENT_2", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "SENT_3", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "SENT_4", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "SENT_5", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "SENT_6", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "SENT_7", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "SENT_8", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "SENT_9", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "SENT_10", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "SENT_11", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "SENT_12", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "SENT_13", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "SENT_14", "width": 1}, {"arrows": "to", "from": "/home/<USER>/Downloads/hypergraph_extractor/input.txt", "to": "SENT_15", "width": 1}, {"arrows": "to", "from": "F1_SENT_1_input_txt", "title": "agent", "to": "E1", "width": 1}, {"arrows": "to", "from": "F1_SENT_1_input_txt", "title": "action", "to": "E2", "width": 1}, {"arrows": "to", "from": "F1_SENT_1_input_txt", "title": "target", "to": "E3", "width": 1}, {"arrows": "to", "from": "F2_SENT_1_input_txt", "title": "agent", "to": "E1", "width": 1}, {"arrows": "to", "from": "F2_SENT_1_input_txt", "title": "action", "to": "E2", "width": 1}, {"arrows": "to", "from": "F2_SENT_1_input_txt", "title": "target", "to": "E4", "width": 1}, {"arrows": "to", "from": "F3_SENT_1_input_txt", "title": "agent", "to": "E1", "width": 1}, {"arrows": "to", "from": "F3_SENT_1_input_txt", "title": "action", "to": "E2", "width": 1}, {"arrows": "to", "from": "F3_SENT_1_input_txt", "title": "target", "to": "E5", "width": 1}, {"arrows": "to", "from": "F4_SENT_1_input_txt", "title": "agent", "to": "E6", "width": 1}, {"arrows": "to", "from": "F4_SENT_1_input_txt", "title": "co_agent", "to": "E7", "width": 1}, {"arrows": "to", "from": "F4_SENT_1_input_txt", "title": "target", "to": "E1", "width": 1}, {"arrows": "to", "from": "F5_SENT_1_input_txt", "title": "target", "to": "E1", "width": 1}, {"arrows": "to", "from": "F5_SENT_1_input_txt", "title": "action", "to": "E9", "width": 1}, {"arrows": "to", "from": "F6_SENT_1_input_txt", "title": "agent", "to": "E1", "width": 1}, {"arrows": "to", "from": "F6_SENT_1_input_txt", "title": "action", "to": "E9", "width": 1}, {"arrows": "to", "from": "F6_SENT_1_input_txt", "title": "target", "to": "E8", "width": 1}, {"arrows": "to", "from": "F7_SENT_1_input_txt", "title": "agent", "to": "E1", "width": 1}, {"arrows": "to", "from": "F7_SENT_1_input_txt", "title": "action", "to": "E10", "width": 1}, {"arrows": "to", "from": "F7_SENT_1_input_txt", "title": "target", "to": "E8", "width": 1}, {"arrows": "to", "from": "F8_SENT_1_input_txt", "title": "agent", "to": "E11", "width": 1}, {"arrows": "to", "from": "F8_SENT_1_input_txt", "title": "target", "to": "E1", "width": 1}, {"arrows": "to", "from": "F9_SENT_1_input_txt", "title": "agent", "to": "E11", "width": 1}, {"arrows": "to", "from": "F9_SENT_1_input_txt", "title": "action", "to": "E12", "width": 1}, {"arrows": "to", "from": "F10_SENT_2_input_txt", "title": "agent", "to": "E11", "width": 1}, {"arrows": "to", "from": "F10_SENT_2_input_txt", "title": "target", "to": "E8", "width": 1}, {"arrows": "to", "from": "F11_SENT_2_input_txt", "title": "agent", "to": "E11", "width": 1}, {"arrows": "to", "from": "F11_SENT_2_input_txt", "title": "target", "to": "E13", "width": 1}, {"arrows": "to", "from": "F12_SENT_2_input_txt", "title": "agent", "to": "E11", "width": 1}, {"arrows": "to", "from": "F12_SENT_2_input_txt", "title": "target", "to": "E14", "width": 1}, {"arrows": "to", "from": "F13_SENT_2_input_txt", "title": "agent", "to": "E15", "width": 1}, {"arrows": "to", "from": "F13_SENT_2_input_txt", "title": "target", "to": "E17", "width": 1}, {"arrows": "to", "from": "F14_SENT_2_input_txt", "title": "agent", "to": "E16", "width": 1}, {"arrows": "to", "from": "F14_SENT_2_input_txt", "title": "target", "to": "E17", "width": 1}, {"arrows": "to", "from": "F15_SENT_2_input_txt", "title": "agent", "to": "E1", "width": 1}, {"arrows": "to", "from": "F15_SENT_2_input_txt", "title": "target", "to": "E4", "width": 1}, {"arrows": "to", "from": "F16_SENT_2_input_txt", "title": "agent", "to": "E1", "width": 1}, {"arrows": "to", "from": "F16_SENT_2_input_txt", "title": "target", "to": "E5", "width": 1}, {"arrows": "to", "from": "F17_SENT_2_input_txt", "title": "target", "to": "E6", "width": 1}, {"arrows": "to", "from": "F17_SENT_2_input_txt", "title": "action", "to": "E7", "width": 1}, {"arrows": "to", "color": "purple", "from": "F18_SENT_2_input_txt", "label": "implies", "to": "F136_SENT_15_input_txt", "width": 1}, {"arrows": "to", "color": "purple", "from": "F18_SENT_2_input_txt", "label": "implies", "to": "F128_SENT_14_input_txt", "width": 1}, {"arrows": "to", "from": "F19_SENT_3_input_txt", "title": "target", "to": "E18", "width": 1}, {"arrows": "to", "from": "F20_SENT_3_input_txt", "title": "target", "to": "E25", "width": 1}, {"arrows": "to", "from": "F21_SENT_3_input_txt", "title": "target", "to": "E26", "width": 1}, {"arrows": "to", "from": "F22_SENT_3_input_txt", "title": "target", "to": "E27", "width": 1}, {"arrows": "to", "color": "purple", "from": "F23_SENT_3_input_txt", "label": "implies", "to": "F132_SENT_15_input_txt", "width": 1}, {"arrows": "to", "color": "purple", "from": "F23_SENT_3_input_txt", "label": "implies", "to": "F133_SENT_15_input_txt", "width": 1}, {"arrows": "to", "from": "F24_SENT_4_input_txt", "title": "agent", "to": "E29", "width": 1}, {"arrows": "to", "from": "F24_SENT_4_input_txt", "title": "effect", "to": "E30", "width": 1}, {"arrows": "to", "from": "F25_SENT_4_input_txt", "title": "agent", "to": "E29", "width": 1}, {"arrows": "to", "from": "F25_SENT_4_input_txt", "title": "effect", "to": "E31", "width": 1}, {"arrows": "to", "from": "F26_SENT_4_input_txt", "title": "agent", "to": "E29", "width": 1}, {"arrows": "to", "from": "F26_SENT_4_input_txt", "title": "effect", "to": "E15", "width": 1}, {"arrows": "to", "from": "F27_SENT_4_input_txt", "title": "agent", "to": "E30", "width": 1}, {"arrows": "to", "from": "F27_SENT_4_input_txt", "title": "comparison", "to": "E32", "width": 1}, {"arrows": "to", "from": "F28_SENT_4_input_txt", "title": "agent", "to": "E30", "width": 1}, {"arrows": "to", "from": "F28_SENT_4_input_txt", "title": "co_agent", "to": "E31", "width": 1}, {"arrows": "to", "from": "F29_SENT_4_input_txt", "title": "agent", "to": "E30", "width": 1}, {"arrows": "to", "from": "F29_SENT_4_input_txt", "title": "co_agent", "to": "E31", "width": 1}, {"arrows": "to", "from": "F29_SENT_4_input_txt", "title": "effect", "to": "E33", "width": 1}, {"arrows": "to", "from": "F30_SENT_4_input_txt", "title": "target", "to": "E34", "width": 1}, {"arrows": "to", "from": "F31_SENT_4_input_txt", "title": "agent", "to": "E36", "width": 1}, {"arrows": "to", "from": "F31_SENT_4_input_txt", "title": "effect", "to": "E30", "width": 1}, {"arrows": "to", "from": "F31_SENT_4_input_txt", "title": "co_effect", "to": "E31", "width": 1}, {"arrows": "to", "from": "F32_SENT_4_input_txt", "title": "agent", "to": "E36", "width": 1}, {"arrows": "to", "from": "F32_SENT_4_input_txt", "title": "effect", "to": "E15", "width": 1}, {"arrows": "to", "from": "F33_SENT_4_input_txt", "title": "agent", "to": "E30", "width": 1}, {"arrows": "to", "from": "F34_SENT_4_input_txt", "title": "agent", "to": "E38", "width": 1}, {"arrows": "to", "from": "F35_SENT_5_input_txt", "title": "target", "to": "E39", "width": 1}, {"arrows": "to", "color": "purple", "from": "F36_SENT_5_input_txt", "label": "implies", "to": "F131_SENT_15_input_txt", "width": 1}, {"arrows": "to", "from": "F37_SENT_5_input_txt", "title": "agent", "to": "E42", "width": 1}, {"arrows": "to", "from": "F37_SENT_5_input_txt", "title": "condition", "to": "E41", "width": 1}, {"arrows": "to", "from": "F37_SENT_5_input_txt", "title": "outcome", "to": "E43", "width": 1}, {"arrows": "to", "from": "F38_SENT_5_input_txt", "title": "agent", "to": "E42", "width": 1}, {"arrows": "to", "from": "F38_SENT_5_input_txt", "title": "condition", "to": "E41", "width": 1}, {"arrows": "to", "from": "F38_SENT_5_input_txt", "title": "effect", "to": "E43", "width": 1}, {"arrows": "to", "from": "F39_SENT_5_input_txt", "title": "agent", "to": "E44", "width": 1}, {"arrows": "to", "from": "F39_SENT_5_input_txt", "title": "target", "to": "E45", "width": 1}, {"arrows": "to", "from": "F40_SENT_5_input_txt", "title": "agent", "to": "E46", "width": 1}, {"arrows": "to", "from": "F40_SENT_5_input_txt", "title": "outcome", "to": "E47", "width": 1}, {"arrows": "to", "from": "F41_SENT_5_input_txt", "title": "agent", "to": "E46", "width": 1}, {"arrows": "to", "from": "F41_SENT_5_input_txt", "title": "effect", "to": "E47", "width": 1}, {"arrows": "to", "from": "F42_SENT_5_input_txt", "title": "agent", "to": "E46", "width": 1}, {"arrows": "to", "from": "F42_SENT_5_input_txt", "title": "effect", "to": "E48", "width": 1}, {"arrows": "to", "from": "F43_SENT_5_input_txt", "title": "agent", "to": "E46", "width": 1}, {"arrows": "to", "from": "F43_SENT_5_input_txt", "title": "effect", "to": "E49", "width": 1}, {"arrows": "to", "from": "F44_SENT_5_input_txt", "title": "agent", "to": "E50", "width": 1}, {"arrows": "to", "from": "F44_SENT_5_input_txt", "title": "target", "to": "E30", "width": 1}, {"arrows": "to", "from": "F45_SENT_5_input_txt", "title": "agent", "to": "E50", "width": 1}, {"arrows": "to", "from": "F45_SENT_5_input_txt", "title": "target", "to": "E31", "width": 1}, {"arrows": "to", "from": "F46_SENT_5_input_txt", "title": "agent", "to": "E50", "width": 1}, {"arrows": "to", "from": "F46_SENT_5_input_txt", "title": "target", "to": "E51", "width": 1}, {"arrows": "to", "from": "F47_SENT_5_input_txt", "title": "agent", "to": "E52", "width": 1}, {"arrows": "to", "from": "F47_SENT_5_input_txt", "title": "effect", "to": "E50", "width": 1}, {"arrows": "to", "from": "F48_SENT_5_input_txt", "title": "agent", "to": "E53", "width": 1}, {"arrows": "to", "from": "F48_SENT_5_input_txt", "title": "effect", "to": "E50", "width": 1}, {"arrows": "to", "from": "F49_SENT_5_input_txt", "title": "agent", "to": "E54", "width": 1}, {"arrows": "to", "from": "F49_SENT_5_input_txt", "title": "effect", "to": "E50", "width": 1}, {"arrows": "to", "from": "F50_SENT_5_input_txt", "title": "agent", "to": "E55", "width": 1}, {"arrows": "to", "from": "F50_SENT_5_input_txt", "title": "effect", "to": "E50", "width": 1}, {"arrows": "to", "from": "F51_SENT_6_input_txt", "title": "agent", "to": "E56", "width": 1}, {"arrows": "to", "from": "F51_SENT_6_input_txt", "title": "target", "to": "E57", "width": 1}, {"arrows": "to", "from": "F52_SENT_6_input_txt", "title": "agent", "to": "E30", "width": 1}, {"arrows": "to", "from": "F52_SENT_6_input_txt", "title": "comparator", "to": "E31", "width": 1}, {"arrows": "to", "from": "F52_SENT_6_input_txt", "title": "condition", "to": "E56", "width": 1}, {"arrows": "to", "from": "F53_SENT_6_input_txt", "title": "agent", "to": "E58", "width": 1}, {"arrows": "to", "from": "F53_SENT_6_input_txt", "title": "condition", "to": "E56", "width": 1}, {"arrows": "to", "from": "F54_SENT_6_input_txt", "title": "agent", "to": "E56", "width": 1}, {"arrows": "to", "from": "F54_SENT_6_input_txt", "title": "effect", "to": "E30", "width": 1}, {"arrows": "to", "from": "F54_SENT_6_input_txt", "title": "effect", "to": "E31", "width": 1}, {"arrows": "to", "from": "F55_SENT_6_input_txt", "title": "agent", "to": "E56", "width": 1}, {"arrows": "to", "from": "F55_SENT_6_input_txt", "title": "effect", "to": "E58", "width": 1}, {"arrows": "to", "from": "F56_SENT_6_input_txt", "title": "agent", "to": "E64", "width": 1}, {"arrows": "to", "from": "F56_SENT_6_input_txt", "title": "co_agent", "to": "E65", "width": 1}, {"arrows": "to", "from": "F56_SENT_6_input_txt", "title": "action", "to": "E61", "width": 1}, {"arrows": "to", "from": "F56_SENT_6_input_txt", "title": "criteria", "to": "E59", "width": 1}, {"arrows": "to", "from": "F56_SENT_6_input_txt", "title": "criteria", "to": "E60", "width": 1}, {"arrows": "to", "from": "F57_SENT_6_input_txt", "title": "action", "to": "E62", "width": 1}, {"arrows": "to", "from": "F57_SENT_6_input_txt", "title": "action", "to": "E63", "width": 1}, {"arrows": "to", "color": "purple", "from": "F58_SENT_6_input_txt", "label": "implies", "to": "F136_SENT_15_input_txt", "width": 1}, {"arrows": "to", "color": "purple", "from": "F58_SENT_6_input_txt", "label": "implies", "to": "F128_SENT_14_input_txt", "width": 1}, {"arrows": "to", "from": "F59_SENT_7_input_txt", "title": "agent", "to": "E66", "width": 1}, {"arrows": "to", "from": "F59_SENT_7_input_txt", "title": "target", "to": "E45", "width": 1}, {"arrows": "to", "from": "F59_SENT_7_input_txt", "title": "target", "to": "E30", "width": 1}, {"arrows": "to", "from": "F59_SENT_7_input_txt", "title": "condition", "to": "E67", "width": 1}, {"arrows": "to", "from": "F59_SENT_7_input_txt", "title": "action", "to": "E72", "width": 1}, {"arrows": "to", "from": "F60_SENT_7_input_txt", "title": "agent", "to": "E39", "width": 1}, {"arrows": "to", "from": "F60_SENT_7_input_txt", "title": "target", "to": "E68", "width": 1}, {"arrows": "to", "from": "F60_SENT_7_input_txt", "title": "condition", "to": "E69", "width": 1}, {"arrows": "to", "from": "F60_SENT_7_input_txt", "title": "context", "to": "E67", "width": 1}, {"arrows": "to", "from": "F60_SENT_7_input_txt", "title": "evidence_source", "to": "E74", "width": 1}, {"arrows": "to", "from": "F61_SENT_7_input_txt", "title": "agent", "to": "E39", "width": 1}, {"arrows": "to", "from": "F61_SENT_7_input_txt", "title": "target", "to": "E69", "width": 1}, {"arrows": "to", "from": "F61_SENT_7_input_txt", "title": "context", "to": "E67", "width": 1}, {"arrows": "to", "from": "F61_SENT_7_input_txt", "title": "evidence_source", "to": "E74", "width": 1}, {"arrows": "to", "from": "F62_SENT_7_input_txt", "title": "agent", "to": "E39", "width": 1}, {"arrows": "to", "from": "F62_SENT_7_input_txt", "title": "effect", "to": "E69", "width": 1}, {"arrows": "to", "from": "F62_SENT_7_input_txt", "title": "context", "to": "E67", "width": 1}, {"arrows": "to", "from": "F63_SENT_7_input_txt", "title": "agent", "to": "E70", "width": 1}, {"arrows": "to", "from": "F63_SENT_7_input_txt", "title": "action", "to": "E73", "width": 1}, {"arrows": "to", "from": "F63_SENT_7_input_txt", "title": "condition", "to": "E71", "width": 1}, {"arrows": "to", "from": "F64_SENT_8_input_txt", "title": "agent", "to": "E31", "width": 1}, {"arrows": "to", "from": "F64_SENT_8_input_txt", "title": "co_agent", "to": "E30", "width": 1}, {"arrows": "to", "from": "F64_SENT_8_input_txt", "title": "target", "to": "E79", "width": 1}, {"arrows": "to", "from": "F64_SENT_8_input_txt", "title": "evidence_source", "to": "E75", "width": 1}, {"arrows": "to", "from": "F64_SENT_8_input_txt", "title": "evidence_source", "to": "E76", "width": 1}, {"arrows": "to", "from": "F65_SENT_8_input_txt", "title": "agent", "to": "E75", "width": 1}, {"arrows": "to", "from": "F65_SENT_8_input_txt", "title": "co_agent", "to": "E76", "width": 1}, {"arrows": "to", "from": "F65_SENT_8_input_txt", "title": "target", "to": "E77", "width": 1}, {"arrows": "to", "from": "F66_SENT_8_input_txt", "title": "agent", "to": "E78", "width": 1}, {"arrows": "to", "from": "F66_SENT_8_input_txt", "title": "condition", "to": "E79", "width": 1}, {"arrows": "to", "from": "F67_SENT_8_input_txt", "title": "agent", "to": "E45", "width": 1}, {"arrows": "to", "from": "F67_SENT_8_input_txt", "title": "target", "to": "E80", "width": 1}, {"arrows": "to", "from": "F68_SENT_8_input_txt", "title": "agent", "to": "E80", "width": 1}, {"arrows": "to", "from": "F68_SENT_8_input_txt", "title": "target", "to": "E81", "width": 1}, {"arrows": "to", "from": "F69_SENT_8_input_txt", "title": "agent", "to": "E80", "width": 1}, {"arrows": "to", "from": "F69_SENT_8_input_txt", "title": "target", "to": "E82", "width": 1}, {"arrows": "to", "from": "F70_SENT_8_input_txt", "title": "agent", "to": "E39", "width": 1}, {"arrows": "to", "from": "F70_SENT_8_input_txt", "title": "target", "to": "E82", "width": 1}, {"arrows": "to", "from": "F71_SENT_9_input_txt", "title": "agent", "to": "E39", "width": 1}, {"arrows": "to", "from": "F71_SENT_9_input_txt", "title": "effect", "to": "E84", "width": 1}, {"arrows": "to", "from": "F71_SENT_9_input_txt", "title": "condition", "to": "E71", "width": 1}, {"arrows": "to", "from": "F72_SENT_9_input_txt", "title": "agent", "to": "E85", "width": 1}, {"arrows": "to", "from": "F72_SENT_9_input_txt", "title": "effect", "to": "E86", "width": 1}, {"arrows": "to", "from": "F73_SENT_9_input_txt", "title": "agent", "to": "E86", "width": 1}, {"arrows": "to", "from": "F73_SENT_9_input_txt", "title": "effect", "to": "E31", "width": 1}, {"arrows": "to", "from": "F74_SENT_9_input_txt", "title": "agent", "to": "E31", "width": 1}, {"arrows": "to", "from": "F74_SENT_9_input_txt", "title": "effect", "to": "E30", "width": 1}, {"arrows": "to", "from": "F75_SENT_9_input_txt", "title": "agent", "to": "E85", "width": 1}, {"arrows": "to", "from": "F75_SENT_9_input_txt", "title": "effect", "to": "E87", "width": 1}, {"arrows": "to", "from": "F76_SENT_9_input_txt", "title": "agent", "to": "E87", "width": 1}, {"arrows": "to", "from": "F76_SENT_9_input_txt", "title": "effect", "to": "E88", "width": 1}, {"arrows": "to", "from": "F77_SENT_9_input_txt", "title": "agent", "to": "E85", "width": 1}, {"arrows": "to", "from": "F77_SENT_9_input_txt", "title": "effect", "to": "E8", "width": 1}, {"arrows": "to", "from": "F78_SENT_9_input_txt", "title": "agent", "to": "E90", "width": 1}, {"arrows": "to", "from": "F78_SENT_9_input_txt", "title": "effect", "to": "E89", "width": 1}, {"arrows": "to", "from": "F79_SENT_9_input_txt", "title": "agent", "to": "E91", "width": 1}, {"arrows": "to", "from": "F79_SENT_9_input_txt", "title": "effect", "to": "E89", "width": 1}, {"arrows": "to", "from": "F80_SENT_9_input_txt", "title": "agent", "to": "E85", "width": 1}, {"arrows": "to", "from": "F80_SENT_9_input_txt", "title": "effect", "to": "E92", "width": 1}, {"arrows": "to", "from": "F81_SENT_9_input_txt", "title": "agent", "to": "E92", "width": 1}, {"arrows": "to", "from": "F81_SENT_9_input_txt", "title": "effect", "to": "E89", "width": 1}, {"arrows": "to", "from": "F82_SENT_9_input_txt", "title": "agent", "to": "E89", "width": 1}, {"arrows": "to", "from": "F82_SENT_9_input_txt", "title": "effect", "to": "E93", "width": 1}, {"arrows": "to", "from": "F83_SENT_10_input_txt", "title": "agent", "to": "E31", "width": 1}, {"arrows": "to", "from": "F83_SENT_10_input_txt", "title": "target", "to": "E94", "width": 1}, {"arrows": "to", "from": "F84_SENT_10_input_txt", "title": "biomarker", "to": "E31", "width": 1}, {"arrows": "to", "from": "F84_SENT_10_input_txt", "title": "cohort", "to": "E95", "width": 1}, {"arrows": "to", "from": "F85_SENT_10_input_txt", "title": "biomarker", "to": "E31", "width": 1}, {"arrows": "to", "from": "F85_SENT_10_input_txt", "title": "cohort", "to": "E96", "width": 1}, {"arrows": "to", "from": "F86_SENT_10_input_txt", "title": "biomarker", "to": "E31", "width": 1}, {"arrows": "to", "from": "F86_SENT_10_input_txt", "title": "cohort", "to": "E97", "width": 1}, {"arrows": "to", "from": "F86_SENT_10_input_txt", "title": "exposure", "to": "E98", "width": 1}, {"arrows": "to", "from": "F87_SENT_10_input_txt", "title": "biomarker", "to": "E31", "width": 1}, {"arrows": "to", "from": "F87_SENT_10_input_txt", "title": "cohort", "to": "E99", "width": 1}, {"arrows": "to", "from": "F87_SENT_10_input_txt", "title": "exposure", "to": "E100", "width": 1}, {"arrows": "to", "from": "F88_SENT_10_input_txt", "title": "biomarker", "to": "E31", "width": 1}, {"arrows": "to", "from": "F88_SENT_10_input_txt", "title": "cohort", "to": "E99", "width": 1}, {"arrows": "to", "from": "F88_SENT_10_input_txt", "title": "exposure", "to": "E101", "width": 1}, {"arrows": "to", "from": "F89_SENT_10_input_txt", "title": "biomarker", "to": "E31", "width": 1}, {"arrows": "to", "from": "F89_SENT_10_input_txt", "title": "exposure", "to": "E102", "width": 1}, {"arrows": "to", "from": "F90_SENT_10_input_txt", "title": "agent", "to": "E98", "width": 1}, {"arrows": "to", "from": "F90_SENT_10_input_txt", "title": "effect", "to": "E31", "width": 1}, {"arrows": "to", "from": "F90_SENT_10_input_txt", "title": "cohort", "to": "E97", "width": 1}, {"arrows": "to", "from": "F91_SENT_10_input_txt", "title": "agent", "to": "E100", "width": 1}, {"arrows": "to", "from": "F91_SENT_10_input_txt", "title": "effect", "to": "E31", "width": 1}, {"arrows": "to", "from": "F91_SENT_10_input_txt", "title": "cohort", "to": "E99", "width": 1}, {"arrows": "to", "from": "F92_SENT_10_input_txt", "title": "agent", "to": "E101", "width": 1}, {"arrows": "to", "from": "F92_SENT_10_input_txt", "title": "effect", "to": "E31", "width": 1}, {"arrows": "to", "from": "F92_SENT_10_input_txt", "title": "cohort", "to": "E99", "width": 1}, {"arrows": "to", "from": "F93_SENT_10_input_txt", "title": "agent", "to": "E102", "width": 1}, {"arrows": "to", "from": "F93_SENT_10_input_txt", "title": "effect", "to": "E31", "width": 1}, {"arrows": "to", "from": "F94_SENT_11_input_txt", "title": "agent", "to": "E39", "width": 1}, {"arrows": "to", "from": "F94_SENT_11_input_txt", "title": "target", "to": "E93", "width": 1}, {"arrows": "to", "from": "F94_SENT_11_input_txt", "title": "target", "to": "E103", "width": 1}, {"arrows": "to", "from": "F94_SENT_11_input_txt", "title": "target", "to": "E8", "width": 1}, {"arrows": "to", "from": "F95_SENT_11_input_txt", "title": "agent", "to": "E45", "width": 1}, {"arrows": "to", "from": "F95_SENT_11_input_txt", "title": "agent", "to": "E32", "width": 1}, {"arrows": "to", "from": "F95_SENT_11_input_txt", "title": "target", "to": "E104", "width": 1}, {"arrows": "to", "from": "F96_SENT_11_input_txt", "title": "agent", "to": "E108", "width": 1}, {"arrows": "to", "from": "F96_SENT_11_input_txt", "title": "condition", "to": "E105", "width": 1}, {"arrows": "to", "from": "F96_SENT_11_input_txt", "title": "target", "to": "E106", "width": 1}, {"arrows": "to", "from": "F97_SENT_11_input_txt", "title": "agent", "to": "E108", "width": 1}, {"arrows": "to", "from": "F97_SENT_11_input_txt", "title": "condition", "to": "E105", "width": 1}, {"arrows": "to", "from": "F97_SENT_11_input_txt", "title": "target", "to": "E107", "width": 1}, {"arrows": "to", "from": "F98_SENT_11_input_txt", "title": "agent", "to": "E108", "width": 1}, {"arrows": "to", "from": "F98_SENT_11_input_txt", "title": "condition", "to": "E105", "width": 1}, {"arrows": "to", "from": "F98_SENT_11_input_txt", "title": "target", "to": "E39", "width": 1}, {"arrows": "to", "from": "F99_SENT_11_input_txt", "title": "target", "to": "E109", "width": 1}, {"arrows": "to", "color": "purple", "from": "F100_SENT_11_input_txt", "label": "implies", "to": "F133_SENT_15_input_txt", "width": 1}, {"arrows": "to", "color": "purple", "from": "F100_SENT_11_input_txt", "label": "implies", "to": "F135_SENT_15_input_txt", "width": 1}, {"arrows": "to", "from": "F101_SENT_12_input_txt", "title": "biomarker", "to": "E39", "width": 1}, {"arrows": "to", "from": "F101_SENT_12_input_txt", "title": "condition", "to": "E93", "width": 1}, {"arrows": "to", "from": "F102_SENT_12_input_txt", "title": "biomarker", "to": "E39", "width": 1}, {"arrows": "to", "from": "F102_SENT_12_input_txt", "title": "condition", "to": "E93", "width": 1}, {"arrows": "to", "from": "F103_SENT_12_input_txt", "title": "condition", "to": "E93", "width": 1}, {"arrows": "to", "from": "F103_SENT_12_input_txt", "title": "effect", "to": "E39", "width": 1}, {"arrows": "to", "from": "F104_SENT_12_input_txt", "title": "biomarker", "to": "E39", "width": 1}, {"arrows": "to", "from": "F104_SENT_12_input_txt", "title": "suggested_condition", "to": "E93", "width": 1}, {"arrows": "to", "from": "F105_SENT_12_input_txt", "title": "biomarker", "to": "E39", "width": 1}, {"arrows": "to", "from": "F105_SENT_12_input_txt", "title": "suggested_condition", "to": "E103", "width": 1}, {"arrows": "to", "from": "F106_SENT_12_input_txt", "title": "biomarker", "to": "E39", "width": 1}, {"arrows": "to", "from": "F106_SENT_12_input_txt", "title": "target", "to": "E23", "width": 1}, {"arrows": "to", "from": "F107_SENT_12_input_txt", "title": "biomarker", "to": "E39", "width": 1}, {"arrows": "to", "from": "F107_SENT_12_input_txt", "title": "target", "to": "E8", "width": 1}, {"arrows": "to", "from": "F108_SENT_12_input_txt", "title": "biomarker", "to": "E39", "width": 1}, {"arrows": "to", "from": "F108_SENT_12_input_txt", "title": "target", "to": "E110", "width": 1}, {"arrows": "to", "from": "F109_SENT_12_input_txt", "title": "cohort", "to": "E111", "width": 1}, {"arrows": "to", "from": "F109_SENT_12_input_txt", "title": "biomarker", "to": "E39", "width": 1}, {"arrows": "to", "color": "purple", "from": "F110_SENT_12_input_txt", "label": "implies", "to": "F131_SENT_15_input_txt", "width": 1}, {"arrows": "to", "color": "purple", "from": "F110_SENT_12_input_txt", "label": "implies", "to": "F134_SENT_15_input_txt", "width": 1}, {"arrows": "to", "color": "purple", "from": "F111_SENT_12_input_txt", "label": "implies", "to": "F132_SENT_15_input_txt", "width": 1}, {"arrows": "to", "color": "purple", "from": "F111_SENT_12_input_txt", "label": "implies", "to": "F134_SENT_15_input_txt", "width": 1}, {"arrows": "to", "color": "purple", "from": "F112_SENT_12_input_txt", "label": "implies", "to": "F130_SENT_14_input_txt", "width": 1}, {"arrows": "to", "color": "purple", "from": "F112_SENT_12_input_txt", "label": "implies", "to": "F129_SENT_14_input_txt", "width": 1}, {"arrows": "to", "from": "F113_SENT_13_input_txt", "title": "agent", "to": "E39", "width": 1}, {"arrows": "to", "from": "F113_SENT_13_input_txt", "title": "target", "to": "E113", "width": 1}, {"arrows": "to", "from": "F113_SENT_13_input_txt", "title": "target", "to": "E114", "width": 1}, {"arrows": "to", "from": "F113_SENT_13_input_txt", "title": "condition", "to": "E93", "width": 1}, {"arrows": "to", "from": "F113_SENT_13_input_txt", "title": "condition", "to": "E112", "width": 1}, {"arrows": "to", "from": "F114_SENT_13_input_txt", "title": "agent", "to": "E39", "width": 1}, {"arrows": "to", "from": "F114_SENT_13_input_txt", "title": "target", "to": "E113", "width": 1}, {"arrows": "to", "from": "F114_SENT_13_input_txt", "title": "target", "to": "E114", "width": 1}, {"arrows": "to", "from": "F114_SENT_13_input_txt", "title": "condition", "to": "E93", "width": 1}, {"arrows": "to", "from": "F114_SENT_13_input_txt", "title": "condition", "to": "E112", "width": 1}, {"arrows": "to", "from": "F115_SENT_13_input_txt", "title": "agent", "to": "E39", "width": 1}, {"arrows": "to", "from": "F115_SENT_13_input_txt", "title": "effect", "to": "E118", "width": 1}, {"arrows": "to", "from": "F115_SENT_13_input_txt", "title": "action", "to": "E77", "width": 1}, {"arrows": "to", "from": "F116_SENT_13_input_txt", "title": "target", "to": "E39", "width": 1}, {"arrows": "to", "from": "F116_SENT_13_input_txt", "title": "action", "to": "E77", "width": 1}, {"arrows": "to", "from": "F117_SENT_13_input_txt", "title": "agent", "to": "E38", "width": 1}, {"arrows": "to", "from": "F117_SENT_13_input_txt", "title": "condition", "to": "E115", "width": 1}, {"arrows": "to", "from": "F118_SENT_13_input_txt", "title": "agent", "to": "E115", "width": 1}, {"arrows": "to", "from": "F118_SENT_13_input_txt", "title": "effect", "to": "E38", "width": 1}, {"arrows": "to", "from": "F119_SENT_13_input_txt", "title": "agent", "to": "E32", "width": 1}, {"arrows": "to", "from": "F119_SENT_13_input_txt", "title": "condition", "to": "E116", "width": 1}, {"arrows": "to", "from": "F120_SENT_13_input_txt", "title": "agent", "to": "E116", "width": 1}, {"arrows": "to", "from": "F120_SENT_13_input_txt", "title": "effect", "to": "E32", "width": 1}, {"arrows": "to", "from": "F121_SENT_13_input_txt", "title": "agent", "to": "E32", "width": 1}, {"arrows": "to", "from": "F121_SENT_13_input_txt", "title": "condition", "to": "E116", "width": 1}, {"arrows": "to", "from": "F121_SENT_13_input_txt", "title": "condition", "to": "E117", "width": 1}, {"arrows": "to", "from": "F122_SENT_14_input_txt", "title": "agent", "to": "E119", "width": 1}, {"arrows": "to", "from": "F122_SENT_14_input_txt", "title": "target", "to": "E120", "width": 1}, {"arrows": "to", "from": "F123_SENT_14_input_txt", "title": "target", "to": "E77", "width": 1}, {"arrows": "to", "from": "F123_SENT_14_input_txt", "title": "condition", "to": "E121", "width": 1}, {"arrows": "to", "from": "F124_SENT_14_input_txt", "title": "target", "to": "E77", "width": 1}, {"arrows": "to", "from": "F124_SENT_14_input_txt", "title": "condition", "to": "E84", "width": 1}, {"arrows": "to", "from": "F125_SENT_14_input_txt", "title": "agent", "to": "E119", "width": 1}, {"arrows": "to", "from": "F125_SENT_14_input_txt", "title": "target", "to": "E122", "width": 1}, {"arrows": "to", "from": "F125_SENT_14_input_txt", "title": "condition", "to": "E112", "width": 1}, {"arrows": "to", "from": "F126_SENT_14_input_txt", "title": "agent", "to": "E119", "width": 1}, {"arrows": "to", "from": "F126_SENT_14_input_txt", "title": "target", "to": "E123", "width": 1}, {"arrows": "to", "from": "F126_SENT_14_input_txt", "title": "condition", "to": "E112", "width": 1}, {"arrows": "to", "from": "F127_SENT_14_input_txt", "title": "agent", "to": "E119", "width": 1}, {"arrows": "to", "from": "F127_SENT_14_input_txt", "title": "target", "to": "E124", "width": 1}, {"arrows": "to", "from": "F127_SENT_14_input_txt", "title": "condition", "to": "E112", "width": 1}, {"arrows": "to", "from": "F128_SENT_14_input_txt", "title": "agent", "to": "E112", "width": 1}, {"arrows": "to", "from": "F128_SENT_14_input_txt", "title": "target", "to": "E125", "width": 1}, {"arrows": "to", "from": "F129_SENT_14_input_txt", "title": "agent", "to": "E112", "width": 1}, {"arrows": "to", "from": "F129_SENT_14_input_txt", "title": "target", "to": "E126", "width": 1}, {"arrows": "to", "from": "F130_SENT_14_input_txt", "title": "agent", "to": "E112", "width": 1}, {"arrows": "to", "from": "F130_SENT_14_input_txt", "title": "target", "to": "E127", "width": 1}, {"arrows": "to", "from": "F131_SENT_15_input_txt", "title": "agent", "to": "E128", "width": 1}, {"arrows": "to", "from": "F131_SENT_15_input_txt", "title": "target", "to": "E134", "width": 1}, {"arrows": "to", "from": "F131_SENT_15_input_txt", "title": "condition", "to": "E112", "width": 1}, {"arrows": "to", "from": "F131_SENT_15_input_txt", "title": "component", "to": "E130", "width": 1}, {"arrows": "to", "from": "F131_SENT_15_input_txt", "title": "component", "to": "E131", "width": 1}, {"arrows": "to", "from": "F131_SENT_15_input_txt", "title": "component", "to": "E132", "width": 1}, {"arrows": "to", "from": "F131_SENT_15_input_txt", "title": "component", "to": "E133", "width": 1}, {"arrows": "to", "from": "F131_SENT_15_input_txt", "title": "component", "to": "E16", "width": 1}, {"arrows": "to", "from": "F131_SENT_15_input_txt", "title": "component", "to": "E39", "width": 1}, {"arrows": "to", "from": "F132_SENT_15_input_txt", "title": "agent", "to": "E39", "width": 1}, {"arrows": "to", "from": "F132_SENT_15_input_txt", "title": "target", "to": "E135", "width": 1}, {"arrows": "to", "from": "F132_SENT_15_input_txt", "title": "condition", "to": "E84", "width": 1}, {"arrows": "to", "from": "F133_SENT_15_input_txt", "title": "agent", "to": "E39", "width": 1}, {"arrows": "to", "from": "F133_SENT_15_input_txt", "title": "target", "to": "E135", "width": 1}, {"arrows": "to", "from": "F134_SENT_15_input_txt", "title": "agent", "to": "E39", "width": 1}, {"arrows": "to", "from": "F134_SENT_15_input_txt", "title": "target", "to": "E112", "width": 1}, {"arrows": "to", "from": "F134_SENT_15_input_txt", "title": "condition", "to": "E94", "width": 1}, {"arrows": "to", "from": "F135_SENT_15_input_txt", "title": "target", "to": "E112", "width": 1}, {"arrows": "to", "from": "F135_SENT_15_input_txt", "title": "condition", "to": "E94", "width": 1}, {"arrows": "to", "from": "F135_SENT_15_input_txt", "title": "threshold", "to": "E136", "width": 1}, {"arrows": "to", "color": "purple", "from": "F136_SENT_15_input_txt", "label": "implies", "to": "F134_SENT_15_input_txt", "width": 1}, {"arrows": "to", "color": "purple", "from": "F136_SENT_15_input_txt", "label": "implies", "to": "F135_SENT_15_input_txt", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_1", "title": "mentions", "to": "E1", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_1", "title": "mentions", "to": "E11", "width": 1}, {"arrows": "to", "from": "SENT_1", "title": "contains_fact", "to": "F1_SENT_1_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_1", "title": "contains_fact", "to": "F2_SENT_1_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_1", "title": "contains_fact", "to": "F3_SENT_1_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_1", "title": "contains_fact", "to": "F4_SENT_1_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_1", "title": "contains_fact", "to": "F5_SENT_1_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_1", "title": "contains_fact", "to": "F6_SENT_1_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_1", "title": "contains_fact", "to": "F7_SENT_1_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_1", "title": "contains_fact", "to": "F8_SENT_1_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_1", "title": "contains_fact", "to": "F9_SENT_1_input_txt", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_2", "title": "mentions", "to": "E11", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_2", "title": "mentions", "to": "E70", "width": 1}, {"arrows": "to", "from": "SENT_2", "title": "contains_fact", "to": "F10_SENT_2_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_2", "title": "contains_fact", "to": "F11_SENT_2_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_2", "title": "contains_fact", "to": "F12_SENT_2_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_2", "title": "contains_fact", "to": "F13_SENT_2_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_2", "title": "contains_fact", "to": "F14_SENT_2_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_2", "title": "contains_fact", "to": "F15_SENT_2_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_2", "title": "contains_fact", "to": "F16_SENT_2_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_2", "title": "contains_fact", "to": "F17_SENT_2_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_2", "title": "contains_fact", "to": "F18_SENT_2_input_txt", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_3", "title": "mentions", "to": "E73", "width": 1}, {"arrows": "to", "from": "SENT_3", "title": "contains_fact", "to": "F19_SENT_3_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_3", "title": "contains_fact", "to": "F20_SENT_3_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_3", "title": "contains_fact", "to": "F21_SENT_3_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_3", "title": "contains_fact", "to": "F22_SENT_3_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_3", "title": "contains_fact", "to": "F23_SENT_3_input_txt", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_4", "title": "mentions", "to": "E22", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_4", "title": "mentions", "to": "E29", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_4", "title": "mentions", "to": "E30", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_4", "title": "mentions", "to": "E31", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_4", "title": "mentions", "to": "E32", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_4", "title": "mentions", "to": "E58", "width": 1}, {"arrows": "to", "from": "SENT_4", "title": "contains_fact", "to": "F24_SENT_4_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_4", "title": "contains_fact", "to": "F25_SENT_4_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_4", "title": "contains_fact", "to": "F26_SENT_4_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_4", "title": "contains_fact", "to": "F27_SENT_4_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_4", "title": "contains_fact", "to": "F28_SENT_4_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_4", "title": "contains_fact", "to": "F29_SENT_4_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_4", "title": "contains_fact", "to": "F30_SENT_4_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_4", "title": "contains_fact", "to": "F31_SENT_4_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_4", "title": "contains_fact", "to": "F32_SENT_4_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_4", "title": "contains_fact", "to": "F33_SENT_4_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_4", "title": "contains_fact", "to": "F34_SENT_4_input_txt", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_5", "title": "mentions", "to": "E30", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_5", "title": "mentions", "to": "E31", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_5", "title": "mentions", "to": "E32", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_5", "title": "mentions", "to": "E39", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_5", "title": "mentions", "to": "E44", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_5", "title": "mentions", "to": "E45", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_5", "title": "mentions", "to": "E50", "width": 1}, {"arrows": "to", "from": "SENT_5", "title": "contains_fact", "to": "F35_SENT_5_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_5", "title": "contains_fact", "to": "F36_SENT_5_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_5", "title": "contains_fact", "to": "F37_SENT_5_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_5", "title": "contains_fact", "to": "F38_SENT_5_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_5", "title": "contains_fact", "to": "F39_SENT_5_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_5", "title": "contains_fact", "to": "F40_SENT_5_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_5", "title": "contains_fact", "to": "F41_SENT_5_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_5", "title": "contains_fact", "to": "F42_SENT_5_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_5", "title": "contains_fact", "to": "F43_SENT_5_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_5", "title": "contains_fact", "to": "F44_SENT_5_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_5", "title": "contains_fact", "to": "F45_SENT_5_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_5", "title": "contains_fact", "to": "F46_SENT_5_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_5", "title": "contains_fact", "to": "F47_SENT_5_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_5", "title": "contains_fact", "to": "F48_SENT_5_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_5", "title": "contains_fact", "to": "F49_SENT_5_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_5", "title": "contains_fact", "to": "F50_SENT_5_input_txt", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_6", "title": "mentions", "to": "E3", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_6", "title": "mentions", "to": "E30", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_6", "title": "mentions", "to": "E31", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_6", "title": "mentions", "to": "E56", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_6", "title": "mentions", "to": "E57", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_6", "title": "mentions", "to": "E58", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_6", "title": "mentions", "to": "E59", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_6", "title": "mentions", "to": "E60", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_6", "title": "mentions", "to": "E61", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_6", "title": "mentions", "to": "E62", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_6", "title": "mentions", "to": "E63", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_6", "title": "mentions", "to": "E64", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_6", "title": "mentions", "to": "E65", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_6", "title": "mentions", "to": "E67", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_6", "title": "mentions", "to": "E73", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_6", "title": "mentions", "to": "E84", "width": 1}, {"arrows": "to", "from": "SENT_6", "title": "contains_fact", "to": "F51_SENT_6_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_6", "title": "contains_fact", "to": "F52_SENT_6_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_6", "title": "contains_fact", "to": "F53_SENT_6_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_6", "title": "contains_fact", "to": "F54_SENT_6_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_6", "title": "contains_fact", "to": "F55_SENT_6_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_6", "title": "contains_fact", "to": "F56_SENT_6_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_6", "title": "contains_fact", "to": "F57_SENT_6_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_6", "title": "contains_fact", "to": "F58_SENT_6_input_txt", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_7", "title": "mentions", "to": "E30", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_7", "title": "mentions", "to": "E32", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_7", "title": "mentions", "to": "E39", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_7", "title": "mentions", "to": "E45", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_7", "title": "mentions", "to": "E66", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_7", "title": "mentions", "to": "E67", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_7", "title": "mentions", "to": "E68", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_7", "title": "mentions", "to": "E69", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_7", "title": "mentions", "to": "E70", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_7", "title": "mentions", "to": "E71", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_7", "title": "mentions", "to": "E72", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_7", "title": "mentions", "to": "E73", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_7", "title": "mentions", "to": "E74", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_7", "title": "mentions", "to": "E79", "width": 1}, {"arrows": "to", "from": "SENT_7", "title": "contains_fact", "to": "F59_SENT_7_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_7", "title": "contains_fact", "to": "F60_SENT_7_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_7", "title": "contains_fact", "to": "F61_SENT_7_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_7", "title": "contains_fact", "to": "F62_SENT_7_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_7", "title": "contains_fact", "to": "F63_SENT_7_input_txt", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_8", "title": "mentions", "to": "E30", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_8", "title": "mentions", "to": "E31", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_8", "title": "mentions", "to": "E32", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_8", "title": "mentions", "to": "E39", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_8", "title": "mentions", "to": "E45", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_8", "title": "mentions", "to": "E63", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_8", "title": "mentions", "to": "E75", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_8", "title": "mentions", "to": "E76", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_8", "title": "mentions", "to": "E78", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_8", "title": "mentions", "to": "E79", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_8", "title": "mentions", "to": "E80", "width": 1}, {"arrows": "to", "from": "SENT_8", "title": "contains_fact", "to": "F64_SENT_8_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_8", "title": "contains_fact", "to": "F65_SENT_8_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_8", "title": "contains_fact", "to": "F66_SENT_8_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_8", "title": "contains_fact", "to": "F67_SENT_8_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_8", "title": "contains_fact", "to": "F68_SENT_8_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_8", "title": "contains_fact", "to": "F69_SENT_8_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_8", "title": "contains_fact", "to": "F70_SENT_8_input_txt", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_9", "title": "mentions", "to": "E30", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_9", "title": "mentions", "to": "E31", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_9", "title": "mentions", "to": "E32", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_9", "title": "mentions", "to": "E39", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_9", "title": "mentions", "to": "E45", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_9", "title": "mentions", "to": "E69", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_9", "title": "mentions", "to": "E71", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_9", "title": "mentions", "to": "E73", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_9", "title": "mentions", "to": "E79", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_9", "title": "mentions", "to": "E85", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_9", "title": "mentions", "to": "E87", "width": 1}, {"arrows": "to", "from": "SENT_9", "title": "contains_fact", "to": "F71_SENT_9_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_9", "title": "contains_fact", "to": "F72_SENT_9_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_9", "title": "contains_fact", "to": "F73_SENT_9_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_9", "title": "contains_fact", "to": "F74_SENT_9_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_9", "title": "contains_fact", "to": "F75_SENT_9_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_9", "title": "contains_fact", "to": "F76_SENT_9_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_9", "title": "contains_fact", "to": "F77_SENT_9_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_9", "title": "contains_fact", "to": "F78_SENT_9_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_9", "title": "contains_fact", "to": "F79_SENT_9_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_9", "title": "contains_fact", "to": "F80_SENT_9_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_9", "title": "contains_fact", "to": "F81_SENT_9_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_9", "title": "contains_fact", "to": "F82_SENT_9_input_txt", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_10", "title": "mentions", "to": "E31", "width": 1}, {"arrows": "to", "from": "SENT_10", "title": "contains_fact", "to": "F83_SENT_10_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_10", "title": "contains_fact", "to": "F84_SENT_10_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_10", "title": "contains_fact", "to": "F85_SENT_10_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_10", "title": "contains_fact", "to": "F86_SENT_10_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_10", "title": "contains_fact", "to": "F87_SENT_10_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_10", "title": "contains_fact", "to": "F88_SENT_10_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_10", "title": "contains_fact", "to": "F89_SENT_10_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_10", "title": "contains_fact", "to": "F90_SENT_10_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_10", "title": "contains_fact", "to": "F91_SENT_10_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_10", "title": "contains_fact", "to": "F92_SENT_10_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_10", "title": "contains_fact", "to": "F93_SENT_10_input_txt", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_11", "title": "mentions", "to": "E3", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_11", "title": "mentions", "to": "E32", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_11", "title": "mentions", "to": "E39", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_11", "title": "mentions", "to": "E45", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_11", "title": "mentions", "to": "E103", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_11", "title": "mentions", "to": "E104", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_11", "title": "mentions", "to": "E112", "width": 1}, {"arrows": "to", "from": "SENT_11", "title": "contains_fact", "to": "F94_SENT_11_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_11", "title": "contains_fact", "to": "F95_SENT_11_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_11", "title": "contains_fact", "to": "F96_SENT_11_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_11", "title": "contains_fact", "to": "F97_SENT_11_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_11", "title": "contains_fact", "to": "F98_SENT_11_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_11", "title": "contains_fact", "to": "F99_SENT_11_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_11", "title": "contains_fact", "to": "F100_SENT_11_input_txt", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_12", "title": "mentions", "to": "E3", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_12", "title": "mentions", "to": "E32", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_12", "title": "mentions", "to": "E39", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_12", "title": "mentions", "to": "E45", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_12", "title": "mentions", "to": "E103", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_12", "title": "mentions", "to": "E112", "width": 1}, {"arrows": "to", "from": "SENT_12", "title": "contains_fact", "to": "F101_SENT_12_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_12", "title": "contains_fact", "to": "F102_SENT_12_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_12", "title": "contains_fact", "to": "F103_SENT_12_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_12", "title": "contains_fact", "to": "F104_SENT_12_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_12", "title": "contains_fact", "to": "F105_SENT_12_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_12", "title": "contains_fact", "to": "F106_SENT_12_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_12", "title": "contains_fact", "to": "F107_SENT_12_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_12", "title": "contains_fact", "to": "F108_SENT_12_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_12", "title": "contains_fact", "to": "F109_SENT_12_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_12", "title": "contains_fact", "to": "F110_SENT_12_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_12", "title": "contains_fact", "to": "F111_SENT_12_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_12", "title": "contains_fact", "to": "F112_SENT_12_input_txt", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_13", "title": "mentions", "to": "E32", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_13", "title": "mentions", "to": "E39", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_13", "title": "mentions", "to": "E45", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_13", "title": "mentions", "to": "E58", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_13", "title": "mentions", "to": "E63", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_13", "title": "mentions", "to": "E112", "width": 1}, {"arrows": "to", "from": "SENT_13", "title": "contains_fact", "to": "F113_SENT_13_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_13", "title": "contains_fact", "to": "F114_SENT_13_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_13", "title": "contains_fact", "to": "F115_SENT_13_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_13", "title": "contains_fact", "to": "F116_SENT_13_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_13", "title": "contains_fact", "to": "F117_SENT_13_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_13", "title": "contains_fact", "to": "F118_SENT_13_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_13", "title": "contains_fact", "to": "F119_SENT_13_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_13", "title": "contains_fact", "to": "F120_SENT_13_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_13", "title": "contains_fact", "to": "F121_SENT_13_input_txt", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_14", "title": "mentions", "to": "E63", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_14", "title": "mentions", "to": "E69", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_14", "title": "mentions", "to": "E73", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_14", "title": "mentions", "to": "E112", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_14", "title": "mentions", "to": "E121", "width": 1}, {"arrows": "to", "from": "SENT_14", "title": "contains_fact", "to": "F122_SENT_14_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_14", "title": "contains_fact", "to": "F123_SENT_14_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_14", "title": "contains_fact", "to": "F124_SENT_14_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_14", "title": "contains_fact", "to": "F125_SENT_14_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_14", "title": "contains_fact", "to": "F126_SENT_14_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_14", "title": "contains_fact", "to": "F127_SENT_14_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_14", "title": "contains_fact", "to": "F128_SENT_14_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_14", "title": "contains_fact", "to": "F129_SENT_14_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_14", "title": "contains_fact", "to": "F130_SENT_14_input_txt", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_15", "title": "mentions", "to": "E32", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_15", "title": "mentions", "to": "E39", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_15", "title": "mentions", "to": "E45", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_15", "title": "mentions", "to": "E69", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_15", "title": "mentions", "to": "E112", "width": 1}, {"arrows": "to", "color": "#e0e0e0", "from": "SENT_15", "title": "mentions", "to": "E129", "width": 1}, {"arrows": "to", "from": "SENT_15", "title": "contains_fact", "to": "F131_SENT_15_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_15", "title": "contains_fact", "to": "F132_SENT_15_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_15", "title": "contains_fact", "to": "F133_SENT_15_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_15", "title": "contains_fact", "to": "F134_SENT_15_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_15", "title": "contains_fact", "to": "F135_SENT_15_input_txt", "width": 1}, {"arrows": "to", "from": "SENT_15", "title": "contains_fact", "to": "F136_SENT_15_input_txt", "width": 1}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {
    "configure": {
        "enabled": true,
        "filter": [
            "physics"
        ]
    },
    "edges": {
        "color": {
            "inherit": true
        },
        "smooth": {
            "enabled": true,
            "type": "dynamic"
        }
    },
    "interaction": {
        "dragNodes": true,
        "hideEdgesOnDrag": false,
        "hideNodesOnDrag": false
    },
    "physics": {
        "enabled": true,
        "stabilization": {
            "enabled": true,
            "fit": true,
            "iterations": 1000,
            "onlyDynamicEdges": false,
            "updateInterval": 50
        }
    }
};

                  


                  
                  // if this network requires displaying the configure window,
                  // put it in its div
                  options.configure["container"] = document.getElementById("config");
                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  
                      network.on("stabilizationProgress", function(params) {
                          document.getElementById('loadingBar').removeAttribute("style");
                          var maxWidth = 496;
                          var minWidth = 20;
                          var widthFactor = params.iterations/params.total;
                          var width = Math.max(minWidth,maxWidth * widthFactor);
                          document.getElementById('bar').style.width = width + 'px';
                          document.getElementById('text').innerHTML = Math.round(widthFactor*100) + '%';
                      });
                      network.once("stabilizationIterationsDone", function() {
                          document.getElementById('text').innerHTML = '100%';
                          document.getElementById('bar').style.width = '496px';
                          document.getElementById('loadingBar').style.opacity = 0;
                          // really clean the dom element
                          setTimeout(function () {document.getElementById('loadingBar').style.display = 'none';}, 500);
                      });
                  

                  return network;

              }
              drawGraph();
        </script>
    </body>
</html>