define
attribute name value string;
attribute timestamp value string;
attribute truth_value value double;
attribute a_action value string;
attribute a_co_agent value string;
attribute a_condition value string;
attribute a_outcome value string;
attribute delta value string;
attribute literal value string;
attribute prevalence value string;
entity adverseevent, owns name, plays causes:agent, plays causes:effect;
entity anatomicalstructure, owns name, plays observed_in:target;
entity biomarker, owns name, plays causes:agent, plays causes:co_agent, plays causes:co_effect, plays causes:effect, plays causes:target, plays component_of:agent, plays diagnostic_for:agent, plays diagnostic_for:co_agent, plays differentiates:component, plays dose_dependence:biomarker_role, plays enables:agent, plays enables:target, plays especially_high_levels_in:biomarker_role, plays fails_to_provide:agent, plays greater_than:agent, plays greater_than:comparator, plays highest_levels_in:biomarker_role, plays improves:agent, plays indicator_of:agent, plays is_a:agent, plays is_a:target, plays level_increases:agent, plays normal_or_minimally_elevated:agent, plays not_diagnostic_for:agent, plays not_diagnostic_of:agent, plays not_observed:agent, plays not_reflects:biomarker_role, plays observational:agent, plays observational:co_agent, plays observational:comparison, plays observational:effect, plays observational:target, plays observational_association:agent, plays observational_association:target, plays observed:agent, plays observed_in:biomarker_role, plays provides_information_on:agent, plays recommends:criteria, plays reflects:biomarker_role, plays remains_high_in:biomarker_role, plays requires:agent, plays requires_investigation:target, plays requires_monitoring:agent, plays requires_monitoring:target, plays role_in:agent, plays specificity_decreases:agent, plays suggests:biomarker_role, plays used_for:agent, plays used_for_differentiation:agent, plays useful_for:agent;
entity clinicalaction, owns name, plays causes:action, plays causes:agent, plays diagnostic_for:evidence_source, plays fails_to_provide:target, plays improves:action, plays observed:condition_role, plays precludes:agent, plays precludes:co_agent, plays precludes:target, plays provides_information_on:target, plays recommends:action, plays requires_investigation:action, plays requires_investigation:target, plays requires_monitoring:action, plays role_in:target, plays used_for:action;
entity clinicalfinding, owns name;
entity clinicaltool, owns name, plays differentiates:agent;
entity cohort, owns name, plays causes:cohort_role, plays causes:condition_role, plays especially_high_levels_in:cohort_role, plays highest_levels_in:cohort_role, plays observed_in:cohort_role, plays remains_high_in:cohort_role;
entity concept, owns name, plays causes:effect, plays enables:co_agent, plays requires_investigation:action, plays requires_investigation:target;
entity condition, owns name, plays causes:agent;
entity disease, owns name, plays associated_with:agent, plays associated_with:target, plays causes:agent, plays causes:condition_role, plays causes:context, plays causes:effect, plays diagnostic_for:target, plays differentiates:condition_role, plays differentiates:target, plays greater_than:condition_role, plays improves:target, plays indicator_of:target, plays level_increases:target, plays normal_or_minimally_elevated:condition_role, plays not_diagnostic_for:condition_role, plays not_diagnostic_of:target, plays not_observed:condition_role, plays observational:agent, plays observational:condition_role, plays observational:context, plays observational:target, plays observational_association:agent, plays observational_association:outcome, plays observed:condition_role, plays observed_in:agent, plays observed_in:condition_role, plays provides_information_on:target, plays reflects:target, plays requires_investigation:condition_role, plays requires_investigation:target, plays requires_monitoring:condition_role, plays role_in:condition_role, plays specificity_decreases:condition_role, plays specificity_decreases:target, plays suggests:suggested_condition, plays used_for:target, plays used_for_differentiation:target, plays useful_for:condition_role, plays useful_for:target;
entity drug, owns name, plays causes:agent, plays requires:target, plays requires_monitoring:agent;
entity endpoint, owns name, plays associated_with:target, plays calculates:agent, plays calculates:target, plays causes:target, plays component_of:target, plays indicator_of:agent, plays not_observed:target, plays provides_information_on:target;
entity exposure, owns name, plays causes:agent, plays dose_dependence:exposure_role, plays especially_high_levels_in:exposure_role, plays highest_levels_in:exposure_role, plays not_reflects:target;
entity institution, owns name;
entity organization, owns name, plays recommends:agent, plays recommends:co_agent;
entity phenotype, owns name, plays causes:condition_role, plays observational_association:condition_role;
entity study, owns name, plays observational:evidence_source;
entity threshold, owns name, plays requires_investigation:threshold_role;
relation associated_with, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates target;
relation calculates, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates target;
relation causes, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates action, relates agent, relates co_agent, relates co_effect, relates cohort_role, relates condition_role, relates context, relates effect @card(0..), relates target;
relation component_of, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates target;
relation diagnostic_for, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates co_agent, relates evidence_source @card(0..), relates target;
relation differentiates, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates component @card(0..), relates condition_role, relates target;
relation dose_dependence, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates biomarker_role, relates exposure_role;
relation enables, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates co_agent, relates target;
relation especially_high_levels_in, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates biomarker_role, relates cohort_role, relates exposure_role;
relation fails_to_provide, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates target;
relation greater_than, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates comparator, relates condition_role;
relation highest_levels_in, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates biomarker_role, relates cohort_role, relates exposure_role;
relation improves, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates action, relates agent, relates target;
relation indicator_of, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates target;
relation is_a, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates target;
relation level_increases, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates target;
relation normal_or_minimally_elevated, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates condition_role;
relation not_diagnostic_for, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates condition_role;
relation not_diagnostic_of, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates target;
relation not_observed, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates condition_role @card(0..), relates target @card(0..);
relation not_reflects, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates biomarker_role, relates target;
relation observational, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates co_agent, relates comparison, relates condition_role, relates context, relates effect, relates evidence_source, relates target;
relation observational_association, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates condition_role, relates outcome, relates target;
relation observed, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates condition_role @card(0..);
relation observed_in, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates biomarker_role, relates cohort_role, relates condition_role, relates target;
relation precludes, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates co_agent, relates target;
relation provides_information_on, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates target;
relation recommends, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates action, relates agent, relates co_agent, relates criteria @card(0..);
relation reflects, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates biomarker_role, relates target;
relation remains_high_in, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates biomarker_role, relates cohort_role;
relation requires, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent @card(0..), relates target;
relation requires_investigation, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates action @card(0..), relates condition_role, relates target, relates threshold_role;
relation requires_monitoring, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates action, relates agent, relates condition_role, relates target @card(0..);
relation role_in, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates condition_role, relates target;
relation specificity_decreases, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates condition_role, relates target;
relation suggests, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates biomarker_role, relates suggested_condition;
relation used_for, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates action, relates agent, relates target;
relation used_for_differentiation, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates target @card(0..);
relation useful_for, owns timestamp, owns truth_value, owns a_action, owns a_co_agent, owns a_condition, owns a_outcome, owns delta, owns literal, owns prevalence, relates agent, relates condition_role, relates target;
