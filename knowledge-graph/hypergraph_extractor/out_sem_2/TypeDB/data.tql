insert $e1 isa biomarker, has name "Liver enzymes";
insert $e2 isa clinicalaction, has name "Evaluation";
insert $e3 isa disease, has name "Disease";
insert $e4 isa disease, has name "Hepatitic disorder";
insert $e5 isa disease, has name "Cholestatic disorder";
insert $e6 isa biomarker, has name "Enzyme ratios";
insert $e7 isa concept, has name "Pattern recognition";
insert $e8 isa disease, has name "Liver disease";
insert $e9 isa clinicalaction, has name "Investigation";
insert $e10 isa clinicalaction, has name "Management";
insert $e11 isa biomarker, has name "Liver Function Tests";
insert $e12 isa clinicalaction, has name "Screening blood tests";
insert $e13 isa endpoint, has name "Disease activity";
insert $e14 isa clinicalaction, has name "Blood analysis";
insert $e15 isa biomarker, has name "Bilirubin";
insert $e16 isa biomarker, has name "Albumin";
insert $e17 isa endpoint, has name "Functional capacity of the liver";
insert $e18 isa biomarker, has name "Liver enzyme profile";
insert $e19 isa clinicalaction, has name "History and clinical examination";
insert $e20 isa disease, has name "Alcoholic fatty liver disease";
insert $e21 isa disease, has name "Non-alcoholic fatty liver disease";
insert $e22 isa disease, has name "Cholestasis";
insert $e23 isa disease, has name "Hepatitis";
insert $e24 isa clinicalaction, has name "Diagnosis";
insert $e25 isa biomarker, has name "Absolute liver enzyme levels";
insert $e26 isa biomarker, has name "Liver enzyme ratios";
insert $e27 isa biomarker, has name "Pattern of enzymes";
insert $e28 isa clinicalfinding, has name "Mixed picture";
insert $e29 isa disease, has name "Mechanical biliary obstruction";
insert $e30 isa biomarker, has name "ALP";
insert $e31 isa biomarker, has name "GGT";
insert $e32 isa biomarker, has name "ALT";
insert $e33 isa disease, has name "Hepatobiliary source";
insert $e34 isa concept, has name "Single enzyme elevation";
insert $e35 isa concept, has name "Alternative causes";
insert $e36 isa disease, has name "Choledocholithiasis";
insert $e37 isa disease, has name "Stricture forming disease";
insert $e38 isa biomarker, has name "Liver transaminases";
insert $e39 isa biomarker, has name "AST:ALT ratio";
insert $e40 isa disease, has name "Biliary obstruction";
insert $e41 isa phenotype, has name "Cholestatic picture";
insert $e42 isa biomarker, has name "AST:ALT ratio <1.5";
insert $e43 isa disease, has name "Extrahepatic obstruction";
insert $e44 isa biomarker, has name "ALT titre";
insert $e45 isa biomarker, has name "AST";
insert $e46 isa biomarker, has name "AST:ALT ratio >1.5";
insert $e47 isa disease, has name "Intrahepatic cholestasis";
insert $e48 isa disease, has name "Mechanical cholestasis";
insert $e49 isa disease, has name "Medical cholestasis";
insert $e50 isa disease, has name "Drug-induced cholestasis";
insert $e51 isa biomarker, has name "ALT:ALP ratio <2";
insert $e52 isa drug, has name "Antibiotics";
insert $e53 isa drug, has name "Immunosuppressants";
insert $e54 isa drug, has name "Tricyclic antidepressants";
insert $e55 isa drug, has name "Angiotensin converting enzyme inhibitors";
insert $e56 isa disease, has name "Primary Biliary Cirrhosis";
insert $e57 isa anatomicalstructure, has name "intrahepatic biliary ducts";
insert $e58 isa biomarker, has name "transaminases";
insert $e59 isa biomarker, has name "cholestatic liver enzyme levels";
insert $e60 isa biomarker, has name "antimitochondrial antibodies";
insert $e61 isa clinicalaction, has name "diagnosis of PBC";
insert $e62 isa clinicalaction, has name "imaging";
insert $e63 isa clinicalaction, has name "liver biopsy";
insert $e64 isa organization, has name "European Association for Study of the Liver";
insert $e65 isa organization, has name "American Association for Study of Liver Disease";
insert $e66 isa drug, has name "ursodeoxycholic acid";
insert $e67 isa disease, has name "PBC";
insert $e68 isa biomarker, has name "non-histological indicators of cirrhosis";
insert $e69 isa disease, has name "cirrhosis";
insert $e70 isa biomarker, has name "liver enzymes";
insert $e71 isa disease, has name "Primary Sclerosing Cholangitis";
insert $e72 isa clinicalaction, has name "scoring criteria";
insert $e73 isa clinicalaction, has name "diagnosis";
insert $e74 isa study, has name "recent study";
insert $e75 isa clinicalaction, has name "Endoscopic Retrograde Cholangiopancreatography";
insert $e76 isa clinicalaction, has name "Magnetic Resonance Cholangiopancreatography";
insert $e77 isa clinicalaction, has name "Liver biopsy";
insert $e78 isa biomarker, has name "Transaminase";
insert $e79 isa disease, has name "PSC";
insert $e80 isa endpoint, has name "Mayo Risk Score";
insert $e81 isa endpoint, has name "Disease progression in PSC";
insert $e82 isa disease, has name "Oesophageal varices";
insert $e83 isa disease, has name "Liver diseases";
insert $e84 isa disease, has name "Cirrhosis";
insert $e85 isa drug, has name "Alcohol";
insert $e86 isa biomarker, has name "Hepatic enzymes";
insert $e87 isa biomarker, has name "GGT:ALP ratio";
insert $e88 isa adverseevent, has name "Jaundice";
insert $e89 isa biomarker, has name "Macrocytosis";
insert $e90 isa disease, has name "Folate deficiency";
insert $e91 isa disease, has name "Vitamin B12 deficiency";
insert $e92 isa adverseevent, has name "Bone marrow suppression";
insert $e93 isa disease, has name "Alcoholic liver disease";
insert $e94 isa disease, has name "Alcohol abuse";
insert $e95 isa cohort, has name "Former drinkers";
insert $e96 isa cohort, has name "Current drinkers";
insert $e97 isa cohort, has name "Men";
insert $e98 isa exposure, has name "Daily alcohol consumption";
insert $e99 isa cohort, has name "Women";
insert $e100 isa exposure, has name "Binge drinking";
insert $e101 isa exposure, has name "Alcohol consumption without food";
insert $e102 isa exposure, has name "Alcohol intake (top two quartiles)";
insert $e103 isa disease, has name "Non-alcoholic Fatty Liver Disease";
insert $e104 isa drug, has name "pyridoxal-5\u2019-phosphate (vitamin B6)";
insert $e105 isa cohort, has name "Nutritionally-deficient heavy-drinkers";
insert $e106 isa endpoint, has name "ALT production";
insert $e107 isa endpoint, has name "AST production";
insert $e108 isa condition, has name "Absence of pyridoxal-5\u2019-phosphate (vitamin B6)";
insert $e109 isa concept, has name "Normal AST:ALT ratio <1";
insert $e110 isa exposure, has name "Alcohol consumption";
insert $e111 isa cohort, has name "Heavy-drinker";
insert $e112 isa disease, has name "NAFLD";
insert $e113 isa endpoint, has name "Sensitivity";
insert $e114 isa endpoint, has name "Specificity";
insert $e115 isa clinicalaction, has name "Cessation of alcohol intake";
insert $e116 isa clinicalaction, has name "Admission to hospital";
insert $e117 isa disease, has name "No liver disease";
insert $e118 isa concept, has name "Clinical guide for biopsy need";
insert $e119 isa biomarker, has name "Liver enzyme analysis";
insert $e120 isa clinicalaction, has name "Diagnosis of cirrhosis in NAFLD";
insert $e121 isa disease, has name "NASH";
insert $e122 isa clinicalaction, has name "Early identification of metabolic risk factors";
insert $e123 isa clinicalaction, has name "Modification of metabolic risk factors";
insert $e124 isa clinicalaction, has name "Risk stratification";
insert $e125 isa disease, has name "Hypertension";
insert $e126 isa disease, has name "Hyperlipidaemia";
insert $e127 isa endpoint, has name "Glycaemic control";
insert $e128 isa clinicaltool, has name "Scoring system";
insert $e129 isa institution, has name "Mayo clinic";
insert $e130 isa biomarker, has name "Age";
insert $e131 isa biomarker, has name "Hyperglycemia";
insert $e132 isa biomarker, has name "Body mass index";
insert $e133 isa biomarker, has name "Platelet count";
insert $e134 isa disease, has name "Advanced fibrosis";
insert $e135 isa disease, has name "Underlying disease";
insert $e136 isa threshold, has name "Score >1";
insert $r1 isa used_for (agent: $e1, action: $e2, target: $e3), has timestamp "1900-01-01", has truth_value 0.9;
insert $r2 isa used_for (agent: $e1, action: $e2, target: $e4), has timestamp "1900-01-01", has truth_value 0.9;
insert $r3 isa used_for (agent: $e1, action: $e2, target: $e5), has timestamp "1900-01-01", has truth_value 0.9;
insert $r4 isa enables (agent: $e6, co_agent: $e7, target: $e1), has timestamp "1900-01-01", has truth_value 0.9, has a_outcome "greater information";
insert $r5 isa requires_investigation (target: $e1, action: $e9), has timestamp "1900-01-01", has truth_value 0.6;
insert $r6 isa improves (agent: $e1, action: $e9, target: $e8), has timestamp "1900-01-01", has truth_value 0.85;
insert $r7 isa improves (agent: $e1, action: $e10, target: $e8), has timestamp "1900-01-01", has truth_value 0.85;
insert $r8 isa is_a (agent: $e11, target: $e1), has timestamp "1900-01-01", has truth_value 0.9;
insert $r9 isa used_for (agent: $e11, action: $e12), has timestamp "1900-01-01", has truth_value 0.9;
insert $r10 isa provides_information_on (agent: $e11, target: $e8), has timestamp "1900-01-01", has truth_value 0.9;
insert $r11 isa provides_information_on (agent: $e11, target: $e13), has timestamp "1900-01-01", has truth_value 0.9;
insert $r12 isa provides_information_on (agent: $e11, target: $e14), has timestamp "1900-01-01", has truth_value 0.9;
insert $r13 isa provides_information_on (agent: $e15, target: $e17), has timestamp "1900-01-01", has truth_value 0.9;
insert $r14 isa provides_information_on (agent: $e16, target: $e17), has timestamp "1900-01-01", has truth_value 0.9;
insert $r15 isa provides_information_on (agent: $e1, target: $e4), has timestamp "1900-01-01", has truth_value 0.9;
insert $r16 isa provides_information_on (agent: $e1, target: $e5), has timestamp "1900-01-01", has truth_value 0.9;
insert $r17 isa requires_investigation (target: $e6, action: $e7), has timestamp "1900-01-01", has truth_value 0.6;
insert $r18 isa requires_investigation (target: $e18), has timestamp "1900-01-01", has truth_value 0.6, has a_action "assessed in conjunction with a thorough history and clinical examination";
insert $r19 isa requires_investigation (target: $e25), has timestamp "1900-01-01", has truth_value 0.6, has a_action "used to differentiate between alcoholic and non-alcoholic fatty liver disease";
insert $r20 isa requires_investigation (target: $e26), has timestamp "1900-01-01", has truth_value 0.6, has a_action "used to differentiate between alcoholic and non-alcoholic fatty liver disease";
insert $r21 isa requires_investigation (target: $e27), has timestamp "1900-01-01", has truth_value 0.6, has a_action "used to differentiate between cholestasis and hepatitis and aid diagnosis in mixed picture";
insert $r22 isa causes (agent: $e29, effect: $e30), has timestamp "1900-01-01", has truth_value 0.85, has delta "increased";
insert $r23 isa causes (agent: $e29, effect: $e31), has timestamp "1900-01-01", has truth_value 0.85, has delta "increased";
insert $r24 isa causes (agent: $e29, effect: $e15), has timestamp "1900-01-01", has truth_value 0.85, has delta "increased (often)";
insert $r25 isa observational (agent: $e30, comparison: $e32), has timestamp "1900-01-01", has truth_value 0.9, has delta "markedly raised";
insert $r26 isa observational (agent: $e30, co_agent: $e31), has timestamp "1900-01-01", has truth_value 0.9, has delta "elevated in similar proportions";
insert $r27 isa causes (agent: $e30, co_agent: $e31, effect: $e33), has timestamp "1900-01-01", has truth_value 0.85;
insert $r28 isa requires_investigation (target: $e34), has timestamp "1900-01-01", has truth_value 0.5, has a_action "consider alternative causes";
insert $r29 isa causes (agent: $e36, effect: $e30, co_effect: $e31), has timestamp "1900-01-01", has truth_value 0.85, has delta "fluctuate";
insert $r30 isa observational (agent: $e36, effect: $e15), has timestamp "1900-01-01", has truth_value 0.9, has delta "may be normal";
insert $r31 isa observational (agent: $e30), has timestamp "1900-01-01", has truth_value 0.9, has delta "rise and fall gradually";
insert $r32 isa observational (agent: $e38), has timestamp "1900-01-01", has truth_value 0.9, has delta "peaked rise >1000 I/U";
insert $r33 isa requires_investigation (target: $e39), has timestamp "1900-01-01", has truth_value 0.6, has a_action "differentiating the site of biliary obstruction";
insert $r34 isa observational_association (agent: $e42, condition_role: $e41, outcome: $e43), has timestamp "1900-01-01", has truth_value 0.9;
insert $r35 isa causes (agent: $e42, condition_role: $e41, effect: $e43), has timestamp "1900-01-01", has truth_value 0.85;
insert $r36 isa observational_association (agent: $e44, target: $e45), has timestamp "1900-01-01", has truth_value 0.9, has delta "ALT titre is considerably higher than AST";
insert $r37 isa observational_association (agent: $e46, outcome: $e47), has timestamp "1900-01-01", has truth_value 0.9;
insert $r38 isa causes (agent: $e46, effect: $e47), has timestamp "1900-01-01", has truth_value 0.85;
insert $r39 isa causes (agent: $e46, effect: $e48), has timestamp "1900-01-01", has truth_value 0.85;
insert $r40 isa causes (agent: $e46, effect: $e49), has timestamp "1900-01-01", has truth_value 0.85;
insert $r41 isa observational_association (agent: $e50, target: $e30), has timestamp "1900-01-01", has truth_value 0.9, has delta "preferential rise";
insert $r42 isa observational_association (agent: $e50, target: $e31), has timestamp "1900-01-01", has truth_value 0.9, has delta "less rise than ALP";
insert $r43 isa observational_association (agent: $e50, target: $e51), has timestamp "1900-01-01", has truth_value 0.9;
insert $r44 isa causes (agent: $e52, effect: $e50), has timestamp "1900-01-01", has truth_value 0.85;
insert $r45 isa causes (agent: $e53, effect: $e50), has timestamp "1900-01-01", has truth_value 0.85;
insert $r46 isa causes (agent: $e54, effect: $e50), has timestamp "1900-01-01", has truth_value 0.85;
insert $r47 isa causes (agent: $e55, effect: $e50), has timestamp "1900-01-01", has truth_value 0.85;
insert $r48 isa observed_in (agent: $e56, target: $e57), has timestamp "1900-01-01", has truth_value 0.9;
insert $r49 isa greater_than (agent: $e30, comparator: $e31, condition_role: $e56), has timestamp "1900-01-01", has truth_value 0.9;
insert $r50 isa normal_or_minimally_elevated (agent: $e58, condition_role: $e56), has timestamp "1900-01-01", has truth_value 0.9;
insert $r51 isa causes (agent: $e56, effect: $e30, effect: $e31), has timestamp "1900-01-01", has truth_value 0.85;
insert $r52 isa causes (agent: $e56, effect: $e58), has timestamp "1900-01-01", has truth_value 0.85;
insert $r53 isa recommends (agent: $e64, co_agent: $e65, action: $e61, criteria: $e59, criteria: $e60), has timestamp "1900-01-01", has truth_value 0.6;
insert $r54 isa requires_investigation (action: $e62, action: $e63), has timestamp "1900-01-01", has truth_value 0.6, has a_condition "absence of either cholestatic liver enzyme levels or antimitochondrial antibodies";
insert $r55 isa requires_monitoring (agent: $e66, target: $e45, target: $e30, condition_role: $e67, action: $e72), has timestamp "1900-01-01", has truth_value 0.6;
insert $r56 isa observational (agent: $e39, target: $e68, condition_role: $e69, context: $e67, evidence_source: $e74), has timestamp "1900-01-01", has truth_value 0.9, has delta "outperforms";
insert $r57 isa observational (agent: $e39, target: $e69, context: $e67, evidence_source: $e74), has timestamp "1900-01-01", has truth_value 0.9, has delta "low sensitivity", has delta "specificity 65-79%";
insert $r58 isa causes (agent: $e39, effect: $e69, context: $e67), has timestamp "1900-01-01", has truth_value 0.85;
insert $r59 isa requires_monitoring (agent: $e70, action: $e73, condition_role: $e71), has timestamp "1900-01-01", has truth_value 0.6;
insert $r60 isa diagnostic_for (agent: $e31, co_agent: $e30, target: $e79, evidence_source: $e75, evidence_source: $e76), has timestamp "1900-01-01", has truth_value 0.9, has a_condition "other causes of liver disease excluded";
insert $r61 isa precludes (agent: $e75, co_agent: $e76, target: $e77), has timestamp "1900-01-01", has truth_value 0.85;
insert $r62 isa not_diagnostic_for (agent: $e78, condition_role: $e79), has timestamp "1900-01-01", has truth_value 0.9;
insert $r63 isa component_of (agent: $e45, target: $e80), has timestamp "1900-01-01", has truth_value 0.9;
insert $r64 isa calculates (agent: $e80, target: $e81), has timestamp "1900-01-01", has truth_value 0.9;
insert $r65 isa indicator_of (agent: $e80, target: $e82), has timestamp "1900-01-01", has truth_value 0.9;
insert $r66 isa indicator_of (agent: $e39, target: $e82), has timestamp "1900-01-01", has truth_value 0.9, has literal ">1.12";
insert $r67 isa causes (agent: $e39, effect: $e84, condition_role: $e71), has timestamp "1900-01-01", has truth_value 0.85, has delta ">1";
insert $r68 isa causes (agent: $e85, effect: $e86), has timestamp "1900-01-01", has truth_value 0.85;
insert $r69 isa causes (agent: $e86, effect: $e31), has timestamp "1900-01-01", has truth_value 0.85, has delta "raised";
insert $r70 isa causes (agent: $e31, effect: $e30), has timestamp "1900-01-01", has truth_value 0.85, has delta "normal or disproportionately lower than GGT";
insert $r71 isa causes (agent: $e85, effect: $e87), has timestamp "1900-01-01", has truth_value 0.85, has delta ">2.5";
insert $r72 isa causes (agent: $e87, effect: $e88), has timestamp "1900-01-01", has truth_value 0.85, has delta ">2.5";
insert $r73 isa causes (agent: $e85, effect: $e8), has timestamp "1900-01-01", has truth_value 0.85;
insert $r74 isa causes (agent: $e90, effect: $e89), has timestamp "1900-01-01", has truth_value 0.85;
insert $r75 isa causes (agent: $e91, effect: $e89), has timestamp "1900-01-01", has truth_value 0.85;
insert $r76 isa causes (agent: $e85, effect: $e92), has timestamp "1900-01-01", has truth_value 0.85;
insert $r77 isa causes (agent: $e92, effect: $e89), has timestamp "1900-01-01", has truth_value 0.85;
insert $r78 isa causes (agent: $e89, effect: $e93), has timestamp "1900-01-01", has truth_value 0.85;
insert $r79 isa not_diagnostic_of (agent: $e31, target: $e94), has timestamp "1900-01-01", has truth_value 0.9;
insert $r80 isa remains_high_in (biomarker_role: $e31, cohort_role: $e95), has timestamp "1900-01-01", has truth_value 0.9;
insert $r81 isa remains_high_in (biomarker_role: $e31, cohort_role: $e96), has timestamp "1900-01-01", has truth_value 0.9;
insert $r82 isa highest_levels_in (biomarker_role: $e31, cohort_role: $e97, exposure_role: $e98), has timestamp "1900-01-01", has truth_value 0.9;
insert $r83 isa especially_high_levels_in (biomarker_role: $e31, cohort_role: $e99, exposure_role: $e100), has timestamp "1900-01-01", has truth_value 0.9;
insert $r84 isa especially_high_levels_in (biomarker_role: $e31, cohort_role: $e99, exposure_role: $e101), has timestamp "1900-01-01", has truth_value 0.9;
insert $r85 isa dose_dependence (biomarker_role: $e31, exposure_role: $e102), has timestamp "1900-01-01", has truth_value 0.9;
insert $r86 isa causes (agent: $e98, effect: $e31, cohort_role: $e97), has timestamp "1900-01-01", has truth_value 0.85;
insert $r87 isa causes (agent: $e100, effect: $e31, cohort_role: $e99), has timestamp "1900-01-01", has truth_value 0.85;
insert $r88 isa causes (agent: $e101, effect: $e31, cohort_role: $e99), has timestamp "1900-01-01", has truth_value 0.85;
insert $r89 isa causes (agent: $e102, effect: $e31), has timestamp "1900-01-01", has truth_value 0.85;
insert $r90 isa used_for_differentiation (agent: $e39, target: $e93, target: $e103, target: $e8), has timestamp "1900-01-01", has truth_value 0.9;
insert $r91 isa requires (agent: $e45, agent: $e32, target: $e104), has timestamp "1900-01-01", has truth_value 0.9;
insert $r92 isa causes (agent: $e108, condition_role: $e105, target: $e106), has timestamp "1900-01-01", has truth_value 0.85, has delta "decrease";
insert $r93 isa causes (agent: $e108, condition_role: $e105, target: $e107), has timestamp "1900-01-01", has truth_value 0.85, has delta "less decrease";
insert $r94 isa causes (agent: $e108, condition_role: $e105, target: $e39), has timestamp "1900-01-01", has truth_value 0.85, has delta "increase";
insert $r95 isa requires_investigation (target: $e109), has timestamp "1900-01-01", has truth_value 0.6, has a_action "AST:ALT ratio should be <1";
insert $r96 isa observed_in (biomarker_role: $e39, condition_role: $e93), has timestamp "1900-01-01", has truth_value 0.9, has delta ">1", has prevalence 0.92;
insert $r97 isa observed_in (biomarker_role: $e39, condition_role: $e93), has timestamp "1900-01-01", has truth_value 0.9, has delta ">2", has prevalence 0.7;
insert $r98 isa causes (condition_role: $e93, effect: $e39), has timestamp "1900-01-01", has truth_value 0.85, has delta ">2";
insert $r99 isa suggests (biomarker_role: $e39, suggested_condition: $e93), has timestamp "1900-01-01", has truth_value 0.9, has delta ">2";
insert $r100 isa suggests (biomarker_role: $e39, suggested_condition: $e103), has timestamp "1900-01-01", has truth_value 0.9, has delta "<1";
insert $r101 isa reflects (biomarker_role: $e39, target: $e23), has timestamp "1900-01-01", has truth_value 0.9, has delta "high";
insert $r102 isa reflects (biomarker_role: $e39, target: $e8), has timestamp "1900-01-01", has truth_value 0.9, has delta "high";
insert $r103 isa not_reflects (biomarker_role: $e39, target: $e110), has timestamp "1900-01-01", has truth_value 0.9, has delta "high";
insert $r104 isa observed_in (cohort_role: $e111, biomarker_role: $e39), has timestamp "1900-01-01", has truth_value 0.9, has delta "<=1";
insert $r105 isa not_observed (agent: $e39, target: $e113, target: $e114, condition_role: $e93, condition_role: $e112), has timestamp "1900-01-01", has truth_value 0.9;
insert $r106 isa not_observed (agent: $e39, target: $e113, target: $e114, condition_role: $e93, condition_role: $e112), has timestamp "1900-01-01", has truth_value 0.9, has a_co_agent "combination with other factors or models";
insert $r107 isa causes (agent: $e39, effect: $e118, action: $e77), has timestamp "1900-01-01", has truth_value 0.85;
insert $r108 isa requires_investigation (target: $e39, action: $e77), has timestamp "1900-01-01", has truth_value 0.6;
insert $r109 isa observed (agent: $e38, condition_role: $e115), has timestamp "1900-01-01", has truth_value 0.9, has delta "worsen";
insert $r110 isa causes (agent: $e115, effect: $e38), has timestamp "1900-01-01", has truth_value 0.85, has delta "worsen";
insert $r111 isa observed (agent: $e32, condition_role: $e116), has timestamp "1900-01-01", has truth_value 0.9, has delta "rise";
insert $r112 isa causes (agent: $e116, effect: $e32), has timestamp "1900-01-01", has truth_value 0.85, has delta "rise";
insert $r113 isa observed (agent: $e32, condition_role: $e116, condition_role: $e117), has timestamp "1900-01-01", has truth_value 0.9, has delta "rise";
insert $r114 isa fails_to_provide (agent: $e119, target: $e120), has timestamp "1900-01-01", has truth_value 0.9;
insert $r115 isa requires_investigation (target: $e77, condition_role: $e121), has timestamp "1900-01-01", has truth_value 0.6;
insert $r116 isa requires_investigation (target: $e77, condition_role: $e84), has timestamp "1900-01-01", has truth_value 0.6;
insert $r117 isa role_in (agent: $e119, target: $e122, condition_role: $e112), has timestamp "1900-01-01", has truth_value 0.9;
insert $r118 isa role_in (agent: $e119, target: $e123, condition_role: $e112), has timestamp "1900-01-01", has truth_value 0.9;
insert $r119 isa role_in (agent: $e119, target: $e124, condition_role: $e112), has timestamp "1900-01-01", has truth_value 0.9;
insert $r120 isa associated_with (agent: $e112, target: $e125), has timestamp "1900-01-01", has truth_value 0.9;
insert $r121 isa associated_with (agent: $e112, target: $e126), has timestamp "1900-01-01", has truth_value 0.9;
insert $r122 isa associated_with (agent: $e112, target: $e127), has timestamp "1900-01-01", has truth_value 0.9;
insert $r123 isa differentiates (agent: $e128, target: $e134, condition_role: $e112, component: $e130, component: $e131, component: $e132, component: $e133, component: $e16, component: $e39), has timestamp "1900-01-01", has truth_value 0.9;
insert $r124 isa specificity_decreases (agent: $e39, target: $e135, condition_role: $e84), has timestamp "1900-01-01", has truth_value 0.9;
insert $r125 isa level_increases (agent: $e39, target: $e135), has timestamp "1900-01-01", has truth_value 0.9;
insert $r126 isa useful_for (agent: $e39, target: $e112, condition_role: $e94), has timestamp "1900-01-01", has truth_value 0.9;
insert $r127 isa requires_investigation (target: $e112, condition_role: $e94, threshold_role: $e136), has timestamp "1900-01-01", has truth_value 0.6, has a_action "consider cirrhosis";
